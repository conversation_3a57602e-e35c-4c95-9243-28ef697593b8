@echo off
echo ========================================
echo    CS2 Skin Changer - Clean Script
echo ========================================
echo.

echo Limpando arquivos de build...

:: Remove build directory
if exist "build" (
    echo Removendo diretorio build/
    rmdir /s /q build
)

:: Remove bin directory
if exist "bin" (
    echo Removendo diretorio bin/
    rmdir /s /q bin
)

:: Remove lib directory
if exist "lib" (
    echo Removendo diretorio lib/
    rmdir /s /q lib
)

:: Remove Visual Studio files
if exist ".vs" (
    echo Removendo diretorio .vs/
    rmdir /s /q .vs
)

:: Remove CMake cache files
if exist "CMakeCache.txt" del CMakeCache.txt
if exist "cmake_install.cmake" del cmake_install.cmake
if exist "Makefile" del Makefile

:: Remove compiled files
echo Removendo arquivos compilados...
del /s /q *.exe 2>nul
del /s /q *.dll 2>nul
del /s /q *.lib 2>nul
del /s /q *.pdb 2>nul
del /s /q *.ilk 2>nul
del /s /q *.exp 2>nul
del /s /q *.obj 2>nul
del /s /q *.o 2>nul

:: Remove log files
echo Removendo arquivos de log...
del /s /q *.log 2>nul

:: Remove temporary files
echo Removendo arquivos temporarios...
del /s /q *.tmp 2>nul
del /s /q *.temp 2>nul
del /s /q *~ 2>nul

echo.
echo ========================================
echo    Limpeza concluida!
echo ========================================
echo.
echo Todos os arquivos de build foram removidos.
echo Execute build.bat para recompilar o projeto.
echo.
pause
