#pragma once
#include "../core/SkinChanger.h"
#include <memory>
#include <Windows.h>

// Forward declarations for ImGui
struct ImGuiContext;
struct ImDrawData;

class GUI {
public:
    GUI();
    ~GUI();

    // Lifecycle
    bool Initialize(HWND hwnd);
    void Shutdown();
    bool IsInitialized() const { return m_initialized; }
    
    // Rendering
    void BeginFrame();
    void EndFrame();
    void Render();
    
    // State
    void Show() { m_visible = true; }
    void Hide() { m_visible = false; }
    void Toggle() { m_visible = !m_visible; }
    bool IsVisible() const { return m_visible; }
    
    // Set skin changer reference
    void SetSkinChanger(std::shared_ptr<SkinChanger> skinChanger) { m_skinChanger = skinChanger; }
    
    // Window procedure for input handling
    static LRESULT CALLBACK WndProc(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam);
    
private:
    bool m_initialized = false;
    bool m_visible = false;
    HWND m_hwnd = nullptr;
    
    // ImGui context
    ImGuiContext* m_context = nullptr;
    
    // Skin changer reference
    std::shared_ptr<SkinChanger> m_skinChanger;
    
    // UI state
    int m_selectedWeaponTab = 0;
    int m_selectedKnife = 0;
    int m_selectedGlove = 0;
    
    // Rendering methods
    void RenderMainWindow();
    void RenderWeaponsTab();
    void RenderKnivesTab();
    void RenderGlovesTab();
    void RenderSettingsTab();
    
    // Weapon configuration
    void RenderWeaponConfig(WeaponId weaponId, const char* weaponName);
    void RenderSkinSelector(const char* label, SkinId& currentSkin, WeaponId weaponId);
    void RenderWearSlider(const char* label, float& wear);
    void RenderSeedInput(const char* label, int& seed);
    void RenderStatTrakInput(const char* label, int& statTrak);
    void RenderCustomNameInput(const char* label, std::string& customName);
    
    // Utility
    void SetupStyle();
    void SetupColors();
    const char* GetWearText(float wear);
    
    // OpenGL/DirectX rendering
    bool InitializeRenderer();
    void ShutdownRenderer();
};
