#pragma once
#include <Windows.h>
#include <TlHelp32.h>
#include <string>
#include <vector>
#include <memory>

struct ModuleInfo {
    std::string name;
    uintptr_t base;
    size_t size;
    HMODULE handle;
};

class Process {
public:
    Process();
    ~Process();

    // Process management
    bool Attach(const std::string& processName);
    bool Attach(DWORD processId);
    void Detach();
    bool IsValid() const;
    
    // Getters
    HANDLE GetHandle() const { return m_handle; }
    DWORD GetId() const { return m_processId; }
    const std::string& GetName() const { return m_processName; }
    
    // Module management
    bool GetModuleInfo(const std::string& moduleName, ModuleInfo& info);
    std::vector<ModuleInfo> GetModules();
    uintptr_t GetModuleBase(const std::string& moduleName);
    size_t GetModuleSize(const std::string& moduleName);
    
    // Memory operations
    bool ReadMemory(uintptr_t address, void* buffer, size_t size);
    bool WriteMemory(uintptr_t address, const void* buffer, size_t size);
    
    template<typename T>
    T Read(uintptr_t address) {
        T value{};
        ReadMemory(address, &value, sizeof(T));
        return value;
    }
    
    template<typename T>
    bool Write(uintptr_t address, const T& value) {
        return WriteMemory(address, &value, sizeof(T));
    }
    
    // String operations
    std::string ReadString(uintptr_t address, size_t maxLength = 256);
    bool WriteString(uintptr_t address, const std::string& str);
    
    // Pattern scanning
    uintptr_t PatternScan(const std::string& moduleName, const std::string& pattern);
    uintptr_t PatternScan(uintptr_t start, size_t size, const std::string& pattern);
    
    // Utility
    static DWORD GetProcessIdByName(const std::string& processName);
    static std::vector<DWORD> GetProcessIdsByName(const std::string& processName);
    static bool IsProcessRunning(const std::string& processName);
    
private:
    HANDLE m_handle;
    DWORD m_processId;
    std::string m_processName;
    std::vector<ModuleInfo> m_modules;
    
    bool RefreshModules();
    std::vector<uint8_t> PatternToBytes(const std::string& pattern);
    bool ComparePattern(const uint8_t* data, const std::vector<uint8_t>& pattern);
};
