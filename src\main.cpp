#include <iostream>
#include <Windows.h>
#include <thread>
#include <chrono>

#include "core/Application.h"
#include "utils/Console.h"

int main() {
    // Initialize console
    Console::Initialize();
    Console::SetTitle("CS2 Skin Changer v2.0 - Professional Edition");
    
    std::cout << R"(
    ╔══════════════════════════════════════════════════════════════╗
    ║                    CS2 Skin Changer v2.0                    ║
    ║                   Professional Edition                       ║
    ║                                                              ║
    ║  Desenvolvido com foco em estabilidade e segurança          ║
    ║  Suporte completo para skins, facas, luvas e StatTrak       ║
    ╚══════════════════════════════════════════════════════════════╝
    )" << std::endl;

    try {
        // Initialize application
        Application app;
        
        if (!app.Initialize()) {
            Console::Error("Falha ao inicializar a aplicação!");
            Console::Pause();
            return -1;
        }

        Console::Success("Aplicação inicializada com sucesso!");
        Console::Info("Pressione F1 para abrir/fechar o menu");
        Console::Info("Pressione F2 para recarregar configurações");
        Console::Info("Pressione END para sair");

        // Main loop
        app.Run();

    } catch (const std::exception& e) {
        Console::Error("Erro crítico: " + std::string(e.what()));
        Console::Pause();
        return -1;
    }

    Console::Info("Aplicação finalizada.");
    return 0;
}
