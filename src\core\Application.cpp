#include "Application.h"
#include "../utils/Console.h"
#include <Windows.h>
#include <chrono>
#include <thread>

Application::Application() {
    m_memory = std::make_shared<Memory>();
    m_skinChanger = std::make_unique<SkinChanger>();
}

Application::~Application() {
    Shutdown();
}

bool Application::Initialize() {
    Console::Info("Inicializando aplicação...");
    
    if (!InitializeMemory()) {
        Console::Error("Falha ao inicializar memória");
        return false;
    }
    
    if (!InitializeSkinChanger()) {
        Console::Error("Falha ao inicializar skin changer");
        return false;
    }
    
    m_initialized = true;
    Console::Success("Aplicação inicializada com sucesso!");
    return true;
}

void Application::Run() {
    if (!m_initialized) {
        Console::Error("Aplicação não foi inicializada");
        return;
    }
    
    m_running = true;
    
    // Start threads
    m_updateThread = std::thread(&Application::UpdateLoop, this);
    m_inputThread = std::thread(&Application::InputLoop, this);
    
    Console::Info("Aplicação rodando...");
    Console::Info("Pressione F1 para abrir/fechar menu");
    Console::Info("Pressione END para sair");
    
    // Wait for threads to finish
    if (m_updateThread.joinable()) {
        m_updateThread.join();
    }
    
    if (m_inputThread.joinable()) {
        m_inputThread.join();
    }
}

void Application::Shutdown() {
    if (m_running) {
        Console::Info("Finalizando aplicação...");
        m_running = false;
        
        // Wait for threads to finish
        if (m_updateThread.joinable()) {
            m_updateThread.join();
        }
        
        if (m_inputThread.joinable()) {
            m_inputThread.join();
        }
        
        // Shutdown components
        if (m_skinChanger) {
            m_skinChanger->Shutdown();
        }
        
        if (m_memory) {
            m_memory->Detach();
        }
        
        m_initialized = false;
        Console::Success("Aplicação finalizada");
    }
}

bool Application::InitializeMemory() {
    Console::Info("Inicializando sistema de memória...");
    
    if (!m_memory->AttachToCS2()) {
        return false;
    }
    
    Console::Success("Sistema de memória inicializado");
    return true;
}

bool Application::InitializeSkinChanger() {
    Console::Info("Inicializando skin changer...");
    
    if (!m_skinChanger->Initialize(m_memory)) {
        return false;
    }
    
    // Configure some default skins for demonstration
    SkinConfig ak47Config;
    ak47Config.weaponId = WeaponId::AK47;
    ak47Config.skinId = SkinId::AK47_REDLINE;
    ak47Config.seed = 661;
    ak47Config.wear = 0.15f;
    ak47Config.statTrak = 1337;
    ak47Config.customName = "AK-47 | Redline";
    ak47Config.enabled = true;
    m_skinChanger->SetSkinConfig(WeaponId::AK47, ak47Config);
    
    SkinConfig awpConfig;
    awpConfig.weaponId = WeaponId::AWP;
    awpConfig.skinId = SkinId::AWP_DRAGON_LORE;
    awpConfig.seed = 0;
    awpConfig.wear = 0.0001f;
    awpConfig.statTrak = 420;
    awpConfig.customName = "AWP | Dragon Lore";
    awpConfig.enabled = true;
    m_skinChanger->SetSkinConfig(WeaponId::AWP, awpConfig);
    
    // Set default knife
    m_skinChanger->SetKnife(WeaponId::KNIFE_KARAMBIT, SkinId::KNIFE_DOPPLER_PHASE2);
    
    // Set default gloves
    m_skinChanger->SetGloves(WeaponId::GLOVE_SPORTY, SkinId::GLOVE_PANDORAS_BOX);
    
    Console::Success("Skin changer inicializado com configurações padrão");
    return true;
}

void Application::UpdateLoop() {
    Console::Info("Thread de atualização iniciada");
    
    while (m_running) {
        try {
            // Check if CS2 is still running
            if (!m_memory->IsAttached()) {
                Console::Warning("CS2 não está mais rodando, tentando reconectar...");
                if (!m_memory->AttachToCS2()) {
                    Console::Error("Falha ao reconectar ao CS2");
                    m_running = false;
                    break;
                }
                
                // Reinitialize skin changer
                m_skinChanger->Shutdown();
                if (!m_skinChanger->Initialize(m_memory)) {
                    Console::Error("Falha ao reinicializar skin changer");
                    m_running = false;
                    break;
                }
            }
            
            // Update skin changer
            m_skinChanger->Update();
            
        } catch (const std::exception& e) {
            Console::Error("Erro no loop de atualização: " + std::string(e.what()));
        }
        
        Sleep(50); // 20 FPS update rate
    }
    
    Console::Info("Thread de atualização finalizada");
}

void Application::InputLoop() {
    Console::Info("Thread de input iniciada");
    
    while (m_running) {
        try {
            HandleInput();
        } catch (const std::exception& e) {
            Console::Error("Erro no loop de input: " + std::string(e.what()));
        }
        
        Sleep(10); // 100 FPS input polling
    }
    
    Console::Info("Thread de input finalizada");
}

void Application::HandleInput() {
    ProcessHotkeys();
}

void Application::ProcessHotkeys() {
    // F1 - Toggle menu
    static bool f1Pressed = false;
    if (IsKeyPressed(VK_F1)) {
        if (!f1Pressed) {
            ToggleMenu();
            f1Pressed = true;
        }
    } else {
        f1Pressed = false;
    }
    
    // F2 - Reload configs
    static bool f2Pressed = false;
    if (IsKeyPressed(VK_F2)) {
        if (!f2Pressed) {
            Console::Info("Recarregando configurações...");
            m_skinChanger->ReloadConfigs();
            m_skinChanger->ForceUpdate();
            f2Pressed = true;
        }
    } else {
        f2Pressed = false;
    }
    
    // END - Exit application
    static bool endPressed = false;
    if (IsKeyPressed(VK_END)) {
        if (!endPressed) {
            Console::Info("Saindo da aplicação...");
            m_running = false;
            endPressed = true;
        }
    } else {
        endPressed = false;
    }
}

void Application::ToggleMenu() {
    m_showMenu = !m_showMenu;
    if (m_showMenu) {
        ShowMenu();
    } else {
        HideMenu();
    }
}

void Application::ShowMenu() {
    Console::Info("Menu aberto (implementação da GUI pendente)");
    // GUI implementation would go here
}

void Application::HideMenu() {
    Console::Info("Menu fechado");
}

bool Application::IsKeyPressed(int vKey) {
    return (GetAsyncKeyState(vKey) & 0x8000) != 0;
}

bool Application::IsKeyDown(int vKey) {
    return (GetKeyState(vKey) & 0x8000) != 0;
}

void Application::Sleep(int milliseconds) {
    std::this_thread::sleep_for(std::chrono::milliseconds(milliseconds));
}
