# 🔧 Guia de Solução de Problemas - CS2 Skin Changer

## ❌ Problema: "Visual Studio compiler não encontrado"

### 🎯 Soluções Rápidas

#### **Opção 1: Configuração Automática (RECOMENDADO)**
```bash
# Execute este comando:
setup_vs_environment.bat
```
Este script procura e configura automaticamente o Visual Studio instalado.

#### **Opção 2: Instalação Automática do Build Tools**
```bash
# Execute como Administrador:
install_build_tools.bat
```
Este script baixa e instala automaticamente o Visual Studio Build Tools.

#### **Opção 3: Usar MinGW como Alternativa**
```bash
# Se não conseguir usar Visual Studio:
build_mingw.bat
```

#### **Opção 4: Developer Command Prompt**
1. Pressione `Win + R`
2. Digite: `cmd`
3. Procure por "Developer Command Prompt" no menu iniciar
4. Execute o build.bat de lá

### 🔍 Verificação Detalhada

Execute para diagnosticar o problema:
```bash
check_setup.bat
```

### 📥 Instalação Manual do Visual Studio

#### **Visual Studio Community (GRATUITO - RECOMENDADO)**
1. Baixe: https://visualstudio.microsoft.com/downloads/
2. Execute o instalador
3. Selecione "Desktop development with C++"
4. Instale com as opções padrão

#### **Visual Studio Build Tools (MÍNIMO)**
1. Baixe: https://visualstudio.microsoft.com/downloads/#build-tools-for-visual-studio-2022
2. Execute o instalador
3. Selecione "C++ build tools"
4. Marque:
   - MSVC v143 compiler toolset
   - Windows 10/11 SDK
   - CMake tools

#### **MinGW (ALTERNATIVA)**
1. Baixe MSYS2: https://www.msys2.org/
2. Instale e abra o terminal MSYS2
3. Execute: `pacman -S mingw-w64-x86_64-gcc`
4. Adicione ao PATH: `C:\msys64\mingw64\bin`

---

## ❌ Problema: "CMake não encontrado"

### 📥 Instalação do CMake

#### **Opção 1: Site Oficial**
1. Baixe: https://cmake.org/download/
2. Baixe a versão "Windows x64 Installer"
3. Execute e marque "Add CMake to PATH"

#### **Opção 2: Chocolatey**
```bash
# Se tiver Chocolatey instalado:
choco install cmake
```

#### **Opção 3: Winget**
```bash
# Windows 10/11 com winget:
winget install Kitware.CMake
```

### ✅ Verificação
```bash
cmake --version
```

---

## ❌ Problema: "CS2 não encontrado"

### 🎮 Soluções

1. **Certifique-se que o CS2 está rodando**
   - Abra o Counter-Strike 2
   - Entre em um servidor ou modo offline

2. **Execute como Administrador**
   - Clique com botão direito no executável
   - Selecione "Executar como administrador"

3. **Verifique o nome do processo**
   - Abra o Gerenciador de Tarefas
   - Procure por "cs2.exe" ou "Counter-Strike 2"

4. **Desative antivírus temporariamente**
   - Alguns antivírus bloqueiam acesso à memória

---

## ❌ Problema: "Falha ao anexar ao processo"

### 🛡️ Soluções de Segurança

1. **Execute como Administrador**
   - Obrigatório para acesso à memória de outros processos

2. **Desative Windows Defender**
   ```bash
   # Temporariamente (como Admin):
   Set-MpPreference -DisableRealtimeMonitoring $true
   ```

3. **Adicione exceção no antivírus**
   - Adicione a pasta do projeto às exceções
   - Adicione o executável às exceções

4. **Verifique outros cheats**
   - Feche outros programas de modificação de jogos
   - Apenas um pode acessar o processo por vez

---

## ❌ Problema: "Offsets desatualizados"

### 🔄 Atualização de Offsets

1. **Baixe offsets atualizados**
   - Visite: https://github.com/a2x/cs2-dumper
   - Baixe os arquivos mais recentes
   - Substitua os arquivos na pasta `output/`

2. **Recompile o projeto**
   ```bash
   clean.bat
   build.bat
   ```

3. **Verifique a versão do CS2**
   - Offsets mudam a cada atualização do jogo
   - Use offsets compatíveis com sua versão

---

## ❌ Problema: "ImGui não encontrado"

### 📦 Download do ImGui

```bash
# Execute este script:
download_imgui.bat
```

### 📥 Download Manual
1. Visite: https://github.com/ocornut/imgui
2. Baixe como ZIP
3. Extraia para `external/imgui/`

---

## ❌ Problema: "Erro de compilação"

### 🔧 Soluções Gerais

1. **Limpe e recompile**
   ```bash
   clean.bat
   build.bat
   ```

2. **Verifique dependências**
   ```bash
   check_setup.bat
   ```

3. **Use modo verbose**
   ```bash
   # Para ver erros detalhados:
   cmake --build build --config Release --verbose
   ```

4. **Verifique espaço em disco**
   - Compilação precisa de ~2GB livres

---

## ❌ Problema: "Aplicação trava ou fecha"

### 🐛 Debug e Diagnóstico

1. **Execute em modo debug**
   - Compile em modo Debug para mais informações
   - Verifique logs no console

2. **Verifique compatibilidade**
   - CS2 atualizado recentemente?
   - Offsets podem estar desatualizados

3. **Teste em servidor offline**
   - Use modo offline primeiro
   - Evite servidores oficiais para testes

4. **Verifique logs**
   - Console mostra erros detalhados
   - Procure por mensagens de erro específicas

---

## 🆘 Ainda com Problemas?

### 📋 Informações para Suporte

Se ainda estiver com problemas, colete estas informações:

1. **Sistema**
   - Versão do Windows
   - Arquitetura (x64/x86)

2. **Software**
   - Versão do Visual Studio/MinGW
   - Versão do CMake
   - Versão do CS2

3. **Erro**
   - Mensagem de erro completa
   - Quando o erro ocorre
   - Passos para reproduzir

4. **Logs**
   - Output do build.bat
   - Output do check_setup.bat
   - Logs do console da aplicação

### 📞 Onde Buscar Ajuda

1. **Documentação**
   - Leia o README.md completo
   - Verifique o CHANGELOG.md

2. **Comunidade**
   - Fóruns de modding do CS2
   - Comunidades de desenvolvimento de cheats

3. **Recursos Online**
   - Stack Overflow para erros de compilação
   - GitHub Issues de projetos similares

---

## ✅ Checklist de Verificação Rápida

Antes de reportar problemas, verifique:

- [ ] Windows 10/11 64-bit
- [ ] Executando como Administrador
- [ ] Visual Studio ou MinGW instalado
- [ ] CMake instalado e no PATH
- [ ] CS2 rodando
- [ ] Antivírus desabilitado/exceção adicionada
- [ ] Offsets atualizados
- [ ] Projeto compilado sem erros
- [ ] ImGui baixado (se usando GUI)

---

**💡 Dica**: A maioria dos problemas são resolvidos executando `setup_vs_environment.bat` seguido de `build.bat` como Administrador.
