@echo off
echo Baixando ImGui...

if not exist "external" mkdir external
cd external

if exist "imgui" (
    echo ImGui ja existe, removendo versao antiga...
    rmdir /s /q imgui
)

echo Clonando ImGui do GitHub...
git clone https://github.com/ocornut/imgui.git

if exist "imgui" (
    echo ImGui baixado com sucesso!
) else (
    echo Falha ao baixar ImGui. Verifique sua conexao com a internet e se o Git esta instalado.
    pause
    exit /b 1
)

echo.
echo ImGui instalado em external/imgui/
echo.
pause
