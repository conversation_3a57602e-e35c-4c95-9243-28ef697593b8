#pragma once
#include <unordered_map>
#include <string>
#include <vector>

// Weapon IDs
enum class WeaponId : int {
    // Knives
    KNIFE_BAYONET = 500,
    KNIFE_FLIP = 505,
    KNIFE_GUT = 506,
    KNIFE_KARAMBIT = 507,
    KNIFE_M9_BAYONET = 508,
    KNIFE_HUNTSMAN = 509,
    KNIFE_FALCHION = 512,
    KNIFE_BOWIE = 514,
    KNIFE_BUTTERFLY = 515,
    KNIFE_SHADOW_DAGGERS = 516,
    KNIFE_URSUS = 519,
    KNIFE_NAVAJA = 520,
    KNIFE_STILETTO = 522,
    KNIFE_TALON = 523,
    KNIFE_SKELETON = 525,
    KNIFE_SURVIVAL = 518,
    KNIFE_NOMAD = 521,
    KNIFE_PARACORD = 517,
    
    // Gloves
    GLOVE_STUDDED_BLOODHOUND = 5027,
    GLOVE_T_SIDE = 5028,
    GLOVE_CT_SIDE = 5029,
    GLOVE_SPORTY = 5030,
    G<PERSON><PERSON>VE_SLICK = 5031,
    GLOVE_LEATHER_WRAP = 5032,
    GLOVE_MOTORCYCLE = 5033,
    GLOVE_SPECIALIST = 5034,
    GLOVE_HYDRA = 5035,
    
    // Rifles
    AK47 = 7,
    M4A4 = 16,
    M4A1_S = 60,
    AWP = 9,
    SCAR20 = 38,
    G3SG1 = 11,
    
    // Pistols
    GLOCK = 2,
    USP_S = 61,
    P2000 = 32,
    DEAGLE = 1,
    FIVE_SEVEN = 3,
    TEC9 = 30,
    CZ75A = 63,
    P250 = 36,
    DUAL_BERETTAS = 2,
    
    // SMGs
    MAC10 = 17,
    MP9 = 34,
    MP7 = 33,
    UMP45 = 24,
    P90 = 19,
    BIZON = 26,
    
    // Shotguns
    NOVA = 35,
    XM1014 = 25,
    SAWED_OFF = 29,
    MAG7 = 27,
    
    // Machine Guns
    NEGEV = 28,
    M249 = 14
};

// Skin IDs (Paint Kits)
enum class SkinId : int {
    // AK-47 Skins
    AK47_REDLINE = 282,
    AK47_VULCAN = 300,
    AK47_FIRE_SERPENT = 180,
    AK47_BLOODSPORT = 656,
    AK47_NEON_RIDER = 433,
    AK47_FUEL_INJECTOR = 586,
    AK47_ASIIMOV = 524,
    AK47_WASTELAND_REBEL = 381,
    AK47_JAGUAR = 316,
    AK47_CASE_HARDENED = 44,
    
    // AWP Skins
    AWP_DRAGON_LORE = 344,
    AWP_MEDUSA = 425,
    AWP_ASIIMOV = 279,
    AWP_LIGHTNING_STRIKE = 242,
    AWP_HYPER_BEAST = 420,
    AWP_REDLINE = 283,
    AWP_BOOM = 174,
    AWP_GRAPHITE = 109,
    AWP_FEVER_DREAM = 653,
    AWP_NEO_NOIR = 649,
    
    // M4A4 Skins
    M4A4_HOWL = 309,
    M4A4_ASIIMOV = 255,
    M4A4_DRAGON_KING = 400,
    M4A4_DESOLATE_SPACE = 584,
    M4A4_BUZZ_KILL = 632,
    M4A4_HELLFIRE = 430,
    M4A4_CYBER_SECURITY = 668,
    M4A4_NEO_NOIR = 648,
    
    // M4A1-S Skins
    M4A1S_KNIGHT = 306,
    M4A1S_HYPER_BEAST = 421,
    M4A1S_GOLDEN_COIL = 474,
    M4A1S_CHANTICOS_FIRE = 361,
    M4A1S_CYREX = 300,
    M4A1S_GUARDIAN = 243,
    M4A1S_ATOMIC_ALLOY = 264,
    M4A1S_DECIMATOR = 653,
    
    // Knife Skins
    KNIFE_DOPPLER_PHASE1 = 415,
    KNIFE_DOPPLER_PHASE2 = 416,
    KNIFE_DOPPLER_PHASE3 = 417,
    KNIFE_DOPPLER_PHASE4 = 418,
    KNIFE_DOPPLER_RUBY = 415,
    KNIFE_DOPPLER_SAPPHIRE = 416,
    KNIFE_DOPPLER_BLACK_PEARL = 417,
    KNIFE_FADE = 38,
    KNIFE_TIGER_TOOTH = 409,
    KNIFE_MARBLE_FADE = 413,
    KNIFE_DAMASCUS_STEEL = 401,
    KNIFE_CRIMSON_WEB = 12,
    KNIFE_CASE_HARDENED = 44,
    KNIFE_SLAUGHTER = 59,
    KNIFE_VANILLA = 0,
    
    // Glove Skins
    GLOVE_CRIMSON_KIMONO = 10006,
    GLOVE_FADE = 10007,
    GLOVE_FOUNDATION = 10008,
    GLOVE_BADLANDS = 10009,
    GLOVE_PANDORAS_BOX = 10010,
    GLOVE_COOL_MINT = 10013,
    GLOVE_FOREST_DDPAT = 10016,
    GLOVE_CRIMSON_WEAVE = 10018,
    GLOVE_LEATHER_WRAP = 10019,
    GLOVE_VICE = 10021,
    GLOVE_SUPERCONDUCTOR = 10024,
    GLOVE_ARID = 10025,
    GLOVE_OVERTAKE = 10027,
    GLOVE_RACING_GREEN = 10030,
    GLOVE_AMPHIBIOUS = 10033,
    GLOVE_BRONZE_MORPH = 10034
};

// Weapon data structure
struct WeaponInfo {
    std::string name;
    WeaponId id;
    std::string modelPath;
    bool isKnife;
    bool isGlove;
    std::vector<std::pair<std::string, SkinId>> skins;
};

class WeaponData {
public:
    static const std::unordered_map<WeaponId, WeaponInfo>& GetWeaponMap();
    static const WeaponInfo* GetWeaponInfo(WeaponId id);
    static std::vector<WeaponId> GetKnives();
    static std::vector<WeaponId> GetGloves();
    static std::vector<WeaponId> GetWeapons(bool includeKnives = true, bool includeGloves = true);
    static std::string GetWeaponName(WeaponId id);
    static std::string GetSkinName(SkinId id);
    
private:
    static std::unordered_map<WeaponId, WeaponInfo> s_weapons;
    static std::unordered_map<SkinId, std::string> s_skinNames;
    static void InitializeData();
};
