@echo off
echo ========================================
echo   Visual Studio Build Tools Installer
echo ========================================
echo.

:: Check if running as administrator
net session >nul 2>&1
if errorlevel 1 (
    echo ERRO: Este script precisa ser executado como Administrador.
    echo Clique com botao direito e selecione "Executar como administrador"
    pause
    exit /b 1
)

echo Este script ira baixar e instalar o Visual Studio Build Tools.
echo.
echo O que sera instalado:
echo - Visual Studio Build Tools 2022
echo - C++ build tools
echo - Windows 10/11 SDK
echo - CMake tools
echo.
echo Tamanho aproximado: ~3GB
echo Tempo estimado: 10-30 minutos
echo.
set /p confirm="Deseja continuar? (S/N): "
if /i not "%confirm%"=="S" (
    echo Instalacao cancelada.
    pause
    exit /b 0
)

echo.
echo Baixando Visual Studio Build Tools...

:: Create temp directory
if not exist "%TEMP%\vs_buildtools" mkdir "%TEMP%\vs_buildtools"
cd /d "%TEMP%\vs_buildtools"

:: Download Build Tools bootstrapper
echo Baixando bootstrapper...
powershell -Command "& {Invoke-WebRequest -Uri 'https://aka.ms/vs/17/release/vs_buildtools.exe' -OutFile 'vs_buildtools.exe'}"

if not exist "vs_buildtools.exe" (
    echo ERRO: Falha ao baixar o instalador.
    echo Verifique sua conexao com a internet.
    pause
    exit /b 1
)

echo.
echo Iniciando instalacao...
echo.
echo IMPORTANTE: 
echo - A instalacao sera feita em modo silencioso
echo - Pode demorar varios minutos
echo - NAO feche esta janela durante a instalacao
echo.

:: Install Build Tools with C++ workload
echo Instalando componentes necessarios...
vs_buildtools.exe --quiet --wait --add Microsoft.VisualStudio.Workload.VCTools --add Microsoft.VisualStudio.Component.VC.Tools.x86.x64 --add Microsoft.VisualStudio.Component.Windows10SDK.19041 --add Microsoft.VisualStudio.Component.VC.CMake.Project

if errorlevel 1 (
    echo.
    echo ERRO: Falha na instalacao.
    echo.
    echo Tente instalar manualmente:
    echo 1. Baixe: https://visualstudio.microsoft.com/downloads/#build-tools-for-visual-studio-2022
    echo 2. Execute e selecione "C++ build tools"
    echo 3. Instale com as opcoes padrao
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo   Instalacao concluida com sucesso!
echo ========================================
echo.

:: Cleanup
cd /d "%~dp0"
rmdir /s /q "%TEMP%\vs_buildtools" 2>nul

echo Visual Studio Build Tools foi instalado.
echo.
echo Proximos passos:
echo 1. Feche e reabra o prompt de comando
echo 2. Execute build.bat para compilar o projeto
echo.
echo OU execute setup_vs_environment.bat para configurar o ambiente
echo.
pause
