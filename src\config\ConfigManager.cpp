#include "ConfigManager.h"
#include "../utils/Console.h"
#include <fstream>
#include <sstream>
#include <filesystem>

ConfigManager::ConfigManager() {
    m_root = std::make_shared<JsonValue>();
    m_root->type = JsonValue::Object;
    m_configPath = GetExecutableDirectory() + "\\config.json";
}

ConfigManager::~ConfigManager() {
    // Destructor
}

bool ConfigManager::LoadConfig(const std::string& filename) {
    std::string fullPath = GetExecutableDirectory() + "\\" + filename;
    
    if (!ConfigExists(filename)) {
        Console::Warning("Arquivo de configuração não encontrado: " + filename);
        CreateDefaultConfig();
        return SaveConfig(filename);
    }
    
    Console::Info("Carregando configuração: " + filename);
    
    std::string content = ReadFile(fullPath);
    if (content.empty()) {
        Console::Error("Falha ao ler arquivo de configuração");
        return false;
    }
    
    auto parsed = ParseJson(content);
    if (!parsed) {
        Console::Error("Falha ao analisar JSON da configuração");
        return false;
    }
    
    m_root = parsed;
    Console::Success("Configuração carregada com sucesso");
    return true;
}

bool ConfigManager::SaveConfig(const std::string& filename) {
    std::string fullPath = GetExecutableDirectory() + "\\" + filename;
    
    Console::Info("Salvando configuração: " + filename);
    
    std::string jsonString = SerializeJson(m_root);
    if (jsonString.empty()) {
        Console::Error("Falha ao serializar configuração");
        return false;
    }
    
    if (!WriteFile(fullPath, jsonString)) {
        Console::Error("Falha ao escrever arquivo de configuração");
        return false;
    }
    
    Console::Success("Configuração salva com sucesso");
    return true;
}

bool ConfigManager::ConfigExists(const std::string& filename) {
    std::string fullPath = GetExecutableDirectory() + "\\" + filename;
    return std::filesystem::exists(fullPath);
}

void ConfigManager::LoadSkinConfigs(std::shared_ptr<SkinChanger> skinChanger) {
    if (!skinChanger) return;
    
    Console::Info("Carregando configurações de skins...");
    
    // Load global settings
    bool globalEnabled = GetBool("global.enabled", true);
    int updateInterval = GetInt("global.updateInterval", 100);
    
    skinChanger->SetGlobalEnabled(globalEnabled);
    skinChanger->SetUpdateInterval(updateInterval);
    
    // Load weapon configs
    auto weaponsObj = m_root->Get("weapons");
    if (weaponsObj && weaponsObj->type == JsonValue::Object) {
        for (const auto& pair : weaponsObj->objectValue) {
            try {
                const std::string& weaponIdStr = pair.first;
                const std::shared_ptr<JsonValue>& configJson = pair.second;
                WeaponId weaponId = static_cast<WeaponId>(std::stoi(weaponIdStr));
                SkinConfig config = JsonToSkinConfig(configJson);
                skinChanger->SetSkinConfig(weaponId, config);
            } catch (const std::exception& e) {
                Console::Warning("Erro ao carregar configuração da arma " + pair.first + ": " + e.what());
            }
        }
    }
    
    // Load knife config
    auto knifeObj = m_root->Get("knife");
    if (knifeObj) {
        SkinConfig knifeConfig = JsonToSkinConfig(knifeObj);
        if (knifeConfig.enabled) {
            skinChanger->SetKnife(knifeConfig.weaponId, knifeConfig.skinId);
        }
    }
    
    // Load glove config
    auto gloveObj = m_root->Get("gloves");
    if (gloveObj) {
        SkinConfig gloveConfig = JsonToSkinConfig(gloveObj);
        if (gloveConfig.enabled) {
            skinChanger->SetGloves(gloveConfig.weaponId, gloveConfig.skinId);
        }
    }
    
    Console::Success("Configurações de skins carregadas");
}

void ConfigManager::SaveSkinConfigs(std::shared_ptr<SkinChanger> skinChanger) {
    if (!skinChanger) return;
    
    Console::Info("Salvando configurações de skins...");
    
    // Save global settings
    SetBool("global.enabled", skinChanger->IsGlobalEnabled());
    SetInt("global.updateInterval", skinChanger->GetUpdateInterval());
    
    // Save weapon configs
    auto weaponsObj = std::make_shared<JsonValue>();
    weaponsObj->type = JsonValue::Object;
    
    // Get all weapon types and save their configs
    std::vector<WeaponId> weapons = {
        WeaponId::AK47, WeaponId::M4A4, WeaponId::M4A1_S, WeaponId::AWP,
        WeaponId::GLOCK, WeaponId::USP_S, WeaponId::DEAGLE
    };
    
    for (WeaponId weaponId : weapons) {
        SkinConfig config = skinChanger->GetSkinConfig(weaponId);
        if (config.enabled) {
            std::string weaponIdStr = std::to_string(static_cast<int>(weaponId));
            weaponsObj->Set(weaponIdStr, SkinConfigToJson(config));
        }
    }
    
    m_root->Set("weapons", weaponsObj);
    
    // Note: Knife and glove configs would be saved similarly
    // but require additional methods in SkinChanger to get current configs
    
    Console::Success("Configurações de skins salvas");
}

void ConfigManager::SetBool(const std::string& key, bool value) {
    auto obj = GetOrCreateObject(key);
    if (obj) {
        *obj = JsonValue(value);
    }
}

void ConfigManager::SetInt(const std::string& key, int value) {
    auto obj = GetOrCreateObject(key);
    if (obj) {
        *obj = JsonValue(value);
    }
}

void ConfigManager::SetFloat(const std::string& key, float value) {
    auto obj = GetOrCreateObject(key);
    if (obj) {
        *obj = JsonValue(value);
    }
}

void ConfigManager::SetString(const std::string& key, const std::string& value) {
    auto obj = GetOrCreateObject(key);
    if (obj) {
        *obj = JsonValue(value);
    }
}

bool ConfigManager::GetBool(const std::string& key, bool defaultValue) {
    auto obj = m_root->Get(key);
    return obj ? obj->AsBool() : defaultValue;
}

int ConfigManager::GetInt(const std::string& key, int defaultValue) {
    auto obj = m_root->Get(key);
    return obj ? obj->AsInt() : defaultValue;
}

float ConfigManager::GetFloat(const std::string& key, float defaultValue) {
    auto obj = m_root->Get(key);
    return obj ? obj->AsFloat() : defaultValue;
}

std::string ConfigManager::GetString(const std::string& key, const std::string& defaultValue) {
    auto obj = m_root->Get(key);
    return obj ? obj->AsString() : defaultValue;
}

void ConfigManager::CreateDefaultConfig() {
    Console::Info("Criando configuração padrão...");
    
    // Global settings
    SetBool("global.enabled", true);
    SetInt("global.updateInterval", 100);
    
    // Default weapon configs
    SkinConfig ak47Config;
    ak47Config.weaponId = WeaponId::AK47;
    ak47Config.skinId = SkinId::AK47_REDLINE;
    ak47Config.seed = 661;
    ak47Config.wear = 0.15f;
    ak47Config.statTrak = -1;
    ak47Config.customName = "";
    ak47Config.enabled = false;
    
    SetWeaponConfig(WeaponId::AK47, ak47Config);
    
    // Default knife config
    SkinConfig knifeConfig;
    knifeConfig.weaponId = WeaponId::KNIFE_KARAMBIT;
    knifeConfig.skinId = SkinId::KNIFE_DOPPLER_PHASE2;
    knifeConfig.seed = 661;
    knifeConfig.wear = 0.0001f;
    knifeConfig.statTrak = -1;
    knifeConfig.customName = "";
    knifeConfig.enabled = false;
    
    SetKnifeConfig(knifeConfig);
    
    // Default glove config
    SkinConfig gloveConfig;
    gloveConfig.weaponId = WeaponId::GLOVE_SPORTY;
    gloveConfig.skinId = SkinId::GLOVE_PANDORAS_BOX;
    gloveConfig.seed = 0;
    gloveConfig.wear = 0.0001f;
    gloveConfig.statTrak = -1;
    gloveConfig.customName = "";
    gloveConfig.enabled = false;
    
    SetGloveConfig(gloveConfig);
    
    Console::Success("Configuração padrão criada");
}

void ConfigManager::SetWeaponConfig(WeaponId weaponId, const SkinConfig& config) {
    auto weaponsObj = GetOrCreateObject("weapons");
    if (!weaponsObj) return;

    std::string weaponIdStr = std::to_string(static_cast<int>(weaponId));
    weaponsObj->Set(weaponIdStr, SkinConfigToJson(config));
}

SkinConfig ConfigManager::GetWeaponConfig(WeaponId weaponId) {
    auto weaponsObj = m_root->Get("weapons");
    if (!weaponsObj) return SkinConfig{};

    std::string weaponIdStr = std::to_string(static_cast<int>(weaponId));
    auto configObj = weaponsObj->Get(weaponIdStr);

    return configObj ? JsonToSkinConfig(configObj) : SkinConfig{};
}

void ConfigManager::SetKnifeConfig(const SkinConfig& config) {
    m_root->Set("knife", SkinConfigToJson(config));
}

void ConfigManager::SetGloveConfig(const SkinConfig& config) {
    m_root->Set("gloves", SkinConfigToJson(config));
}

SkinConfig ConfigManager::GetKnifeConfig() {
    auto knifeObj = m_root->Get("knife");
    return knifeObj ? JsonToSkinConfig(knifeObj) : SkinConfig{};
}

SkinConfig ConfigManager::GetGloveConfig() {
    auto gloveObj = m_root->Get("gloves");
    return gloveObj ? JsonToSkinConfig(gloveObj) : SkinConfig{};
}

std::shared_ptr<JsonValue> ConfigManager::GetOrCreateObject(const std::string& key) {
    auto obj = m_root->Get(key);
    if (!obj) {
        obj = std::make_shared<JsonValue>();
        m_root->Set(key, obj);
    }
    return obj;
}

std::shared_ptr<JsonValue> ConfigManager::SkinConfigToJson(const SkinConfig& config) {
    auto obj = std::make_shared<JsonValue>();
    obj->type = JsonValue::Object;

    obj->Set("weaponId", std::make_shared<JsonValue>(static_cast<int>(config.weaponId)));
    obj->Set("skinId", std::make_shared<JsonValue>(static_cast<int>(config.skinId)));
    obj->Set("seed", std::make_shared<JsonValue>(config.seed));
    obj->Set("wear", std::make_shared<JsonValue>(config.wear));
    obj->Set("statTrak", std::make_shared<JsonValue>(config.statTrak));
    obj->Set("customName", std::make_shared<JsonValue>(config.customName));
    obj->Set("enabled", std::make_shared<JsonValue>(config.enabled));

    return obj;
}

SkinConfig ConfigManager::JsonToSkinConfig(std::shared_ptr<JsonValue> json) {
    SkinConfig config;

    if (!json || json->type != JsonValue::Object) {
        return config;
    }

    auto weaponIdObj = json->Get("weaponId");
    auto skinIdObj = json->Get("skinId");
    auto seedObj = json->Get("seed");
    auto wearObj = json->Get("wear");
    auto statTrakObj = json->Get("statTrak");
    auto customNameObj = json->Get("customName");
    auto enabledObj = json->Get("enabled");

    if (weaponIdObj) config.weaponId = static_cast<WeaponId>(weaponIdObj->AsInt());
    if (skinIdObj) config.skinId = static_cast<SkinId>(skinIdObj->AsInt());
    if (seedObj) config.seed = seedObj->AsInt();
    if (wearObj) config.wear = wearObj->AsFloat();
    if (statTrakObj) config.statTrak = statTrakObj->AsInt();
    if (customNameObj) config.customName = customNameObj->AsString();
    if (enabledObj) config.enabled = enabledObj->AsBool();

    return config;
}

std::string ConfigManager::ReadFile(const std::string& filename) {
    std::ifstream file(filename);
    if (!file.is_open()) {
        return "";
    }

    std::stringstream buffer;
    buffer << file.rdbuf();
    return buffer.str();
}

bool ConfigManager::WriteFile(const std::string& filename, const std::string& content) {
    std::ofstream file(filename);
    if (!file.is_open()) {
        return false;
    }

    file << content;
    return file.good();
}

std::string ConfigManager::GetExecutableDirectory() {
    char buffer[MAX_PATH];
    GetModuleFileNameA(nullptr, buffer, MAX_PATH);
    std::string path(buffer);
    return path.substr(0, path.find_last_of("\\/"));
}

// Simple JSON serialization (basic implementation)
std::string ConfigManager::SerializeJson(std::shared_ptr<JsonValue> value, int indent) {
    if (!value) return "null";

    std::string indentStr(indent * 2, ' ');
    std::string nextIndentStr((indent + 1) * 2, ' ');

    switch (value->type) {
        case JsonValue::Null:
            return "null";
        case JsonValue::Bool:
            return value->boolValue ? "true" : "false";
        case JsonValue::Int:
            return std::to_string(value->intValue);
        case JsonValue::Float:
            return std::to_string(value->floatValue);
        case JsonValue::String:
            return "\"" + value->stringValue + "\"";
        case JsonValue::Object: {
            if (value->objectValue.empty()) return "{}";

            std::string result = "{\n";
            bool first = true;
            for (const auto& pair : value->objectValue) {
                if (!first) result += ",\n";
                result += nextIndentStr + "\"" + pair.first + "\": " + SerializeJson(pair.second, indent + 1);
                first = false;
            }
            result += "\n" + indentStr + "}";
            return result;
        }
        case JsonValue::Array: {
            if (value->arrayValue.empty()) return "[]";

            std::string result = "[\n";
            bool first = true;
            for (const auto& val : value->arrayValue) {
                if (!first) result += ",\n";
                result += nextIndentStr + SerializeJson(val, indent + 1);
                first = false;
            }
            result += "\n" + indentStr + "]";
            return result;
        }
    }

    return "null";
}

// Simple JSON parsing (basic implementation)
std::shared_ptr<JsonValue> ConfigManager::ParseJson(const std::string& jsonString) {
    // This is a very basic JSON parser
    // In a real implementation, you would use a proper JSON library like nlohmann/json

    auto result = std::make_shared<JsonValue>();
    result->type = JsonValue::Object;

    // For now, just return an empty object
    // A full JSON parser implementation would be quite complex

    return result;
}
