#include "AntiDetection.h"
#include "../utils/Console.h"
#include <random>
#include <chrono>
#include <thread>
#include <TlHelp32.h>
#include <Psapi.h>
#include <winternl.h>
#include <algorithm>
#include <intrin.h>

// Custom PEB structure with the fields we need
typedef struct _CUSTOM_PEB {
    BYTE Reserved1[2];
    BYTE BeingDebugged;
    BYTE Reserved2[1];
    PVOID Reserved3[2];
    PVOID Ldr;
    PVOID ProcessParameters;
    PVOID Reserved4[3];
    PVOID AtlThunkSListPtr;
    PVOID Reserved5;
    ULONG Reserved6;
    PVOID Reserved7;
    ULONG Reserved8;
    ULONG AtlThunkSListPtr32;
    PVOID Reserved9[45];
    BYTE Reserved10[96];
    PVOID PostProcessInitRoutine;
    BYTE Reserved11[128];
    PVOID Reserved12[1];
    ULONG SessionId;
    PVOID ProcessHeap;
    ULONG NtGlobalFlag;
} CUSTOM_PEB, *PCUSTOM_PEB;

#ifndef NT_SUCCESS
#define NT_SUCCESS(Status) (((NTSTATUS)(Status)) >= 0)
#endif

AntiDetection::AntiDetection() {
    // Initialize timing pattern for obfuscation
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(50, 200);
    
    for (int i = 0; i < 10; ++i) {
        m_timingPattern.push_back(dis(gen));
    }
    
    // Initialize trusted modules
    m_trustedModules = {
        "kernel32.dll",
        "ntdll.dll",
        "user32.dll",
        "advapi32.dll",
        "client.dll",
        "engine2.dll"
    };
}

AntiDetection::~AntiDetection() {
    Shutdown();
}

bool AntiDetection::Initialize() {
    if (m_initialized) {
        return true;
    }
    
    Console::Info("Inicializando sistema anti-detecção...");
    
    // Perform initial security checks
    if (IsDebuggerPresent()) {
        HandleDetection("Debugger");
        return false;
    }
    
    if (IsVirtualMachine()) {
        HandleDetection("Virtual Machine");
        // Don't fail on VM detection, just log it
    }
    
    if (IsAnalysisEnvironment()) {
        HandleDetection("Analysis Environment");
        return false;
    }
    
    if (!ValidateProcessIntegrity()) {
        HandleDetection("Process Integrity");
        return false;
    }
    
    m_initialized = true;
    Console::Success("Sistema anti-detecção inicializado");
    return true;
}

void AntiDetection::Shutdown() {
    if (m_initialized) {
        Console::Info("Finalizando sistema anti-detecção...");
        m_initialized = false;
    }
}

bool AntiDetection::IsDebuggerPresent() {
    // Multiple debugger detection techniques
    
    // 1. PEB check
    if (CheckPEB()) {
        LogSecurityEvent("Debugger detected via PEB");
        return true;
    }
    
    // 2. Heap flags check
    if (CheckHeapFlags()) {
        LogSecurityEvent("Debugger detected via heap flags");
        return true;
    }
    
    // 3. NtGlobalFlag check
    if (CheckNtGlobalFlag()) {
        LogSecurityEvent("Debugger detected via NtGlobalFlag");
        return true;
    }
    
    // 4. Debug port check
    if (CheckDebugPort()) {
        LogSecurityEvent("Debugger detected via debug port");
        return true;
    }
    
    // 5. Remote debugger check
    if (CheckRemoteDebugger()) {
        LogSecurityEvent("Remote debugger detected");
        return true;
    }
    
    // 6. OutputDebugString check
    if (CheckOutputDebugString()) {
        LogSecurityEvent("Debugger detected via OutputDebugString");
        return true;
    }
    
    return false;
}

bool AntiDetection::IsVirtualMachine() {
    // VM detection techniques
    
    // 1. CPUID check
    if (CheckCPUID()) {
        LogSecurityEvent("VM detected via CPUID");
        return true;
    }
    
    // 2. Registry check
    if (CheckRegistry()) {
        LogSecurityEvent("VM detected via registry");
        return true;
    }
    
    // 3. File system check
    if (CheckFiles()) {
        LogSecurityEvent("VM detected via files");
        return true;
    }
    
    // 4. Services check
    if (CheckServices()) {
        LogSecurityEvent("VM detected via services");
        return true;
    }
    
    // 5. Process check
    if (CheckProcesses()) {
        LogSecurityEvent("VM detected via processes");
        return true;
    }
    
    return false;
}

bool AntiDetection::IsAnalysisEnvironment() {
    // Analysis environment detection
    
    if (CheckAnalysisTools()) {
        LogSecurityEvent("Analysis tools detected");
        return true;
    }
    
    if (CheckMonitoringTools()) {
        LogSecurityEvent("Monitoring tools detected");
        return true;
    }
    
    return false;
}

bool AntiDetection::ValidateMemoryIntegrity() {
    // Check for memory tampering
    
    if (CheckForHooks()) {
        LogSecurityEvent("API hooks detected");
        return false;
    }
    
    if (CheckForInjectedDLLs()) {
        LogSecurityEvent("Injected DLLs detected");
        return false;
    }
    
    return true;
}

bool AntiDetection::ValidateProcessIntegrity() {
    // Validate process state
    
    if (CheckForSuspiciousProcesses()) {
        LogSecurityEvent("Suspicious processes detected");
        return false;
    }
    
    if (!ValidateModuleSignatures()) {
        LogSecurityEvent("Invalid module signatures detected");
        return false;
    }
    
    return true;
}

bool AntiDetection::SafeReadMemory(HANDLE process, uintptr_t address, void* buffer, size_t size) {
    if (!ValidateHandle(process) || !ValidateAddress(address) || !ValidatePointer(buffer)) {
        return false;
    }
    
    // Add timing obfuscation
    if (m_stealthMode) {
        RandomDelay();
    }
    
    SIZE_T bytesRead;
    BOOL result = ReadProcessMemory(process, reinterpret_cast<LPCVOID>(address), buffer, size, &bytesRead);
    
    return result && bytesRead == size;
}

bool AntiDetection::SafeWriteMemory(HANDLE process, uintptr_t address, const void* buffer, size_t size) {
    if (!ValidateHandle(process) || !ValidateAddress(address) || !ValidatePointer(const_cast<void*>(buffer))) {
        return false;
    }
    
    // Add timing obfuscation
    if (m_stealthMode) {
        RandomDelay();
    }
    
    SIZE_T bytesWritten;
    BOOL result = WriteProcessMemory(process, reinterpret_cast<LPVOID>(address), buffer, size, &bytesWritten);
    
    return result && bytesWritten == size;
}

bool AntiDetection::ValidateAddress(uintptr_t address) {
    // Basic address validation
    if (address == 0 || address < 0x10000 || address > 0x7FFFFFFFFFFF) {
        return false;
    }
    
    // Check if address is in validated list (cache)
    for (uintptr_t validAddr : m_validatedAddresses) {
        if (address == validAddr) {
            return true;
        }
    }
    
    // Additional validation can be added here
    return true;
}

bool AntiDetection::ValidatePointer(void* pointer) {
    return pointer != nullptr && reinterpret_cast<uintptr_t>(pointer) > 0x10000;
}

bool AntiDetection::ValidateHandle(HANDLE handle) {
    return handle != nullptr && handle != INVALID_HANDLE_VALUE;
}

void AntiDetection::SleepWithJitter(int baseMs, int jitterMs) {
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(-jitterMs, jitterMs);
    
    int sleepTime = baseMs + dis(gen);
    if (sleepTime > 0) {
        std::this_thread::sleep_for(std::chrono::milliseconds(sleepTime));
    }
}

void AntiDetection::RandomDelay() {
    static int patternIndex = 0;
    int delay = m_timingPattern[patternIndex % m_timingPattern.size()];
    patternIndex++;
    
    std::this_thread::sleep_for(std::chrono::milliseconds(delay));
}

void AntiDetection::HandleDetection(const std::string& detectionType) {
    LogSecurityEvent("DETECTION: " + detectionType);
    
    if (m_validationLevel >= 2) {
        Console::Error("Detecção de segurança: " + detectionType);
        Console::Error("Aplicação será finalizada por motivos de segurança");
        
        // In a real implementation, you might want to:
        // 1. Clear sensitive data
        // 2. Restore original game state
        // 3. Exit gracefully
        
        ExitProcess(1);
    }
}

void AntiDetection::LogSecurityEvent(const std::string& event) {
    Console::Warning("[SECURITY] " + event);
}

// Detection method implementations
bool AntiDetection::CheckPEB() {
    // Check Process Environment Block for debugger flags
    __try {
        CUSTOM_PEB* peb = reinterpret_cast<CUSTOM_PEB*>(__readgsqword(0x60));
        if (peb->BeingDebugged) {
            return true;
        }

        // Check NtGlobalFlag
        if (peb->NtGlobalFlag & 0x70) {
            return true;
        }
    }
    __except (EXCEPTION_EXECUTE_HANDLER) {
        // Exception occurred, might be debugger
        return true;
    }

    return false;
}

bool AntiDetection::CheckHeapFlags() {
    // Check heap flags for debugger presence
    __try {
        CUSTOM_PEB* peb = reinterpret_cast<CUSTOM_PEB*>(__readgsqword(0x60));
        PVOID heap = peb->ProcessHeap;

        DWORD flags = *reinterpret_cast<PDWORD>(reinterpret_cast<PCHAR>(heap) + 0x70);
        DWORD forceFlags = *reinterpret_cast<PDWORD>(reinterpret_cast<PCHAR>(heap) + 0x74);

        if (flags & ~HEAP_GROWABLE || forceFlags != 0) {
            return true;
        }
    }
    __except (EXCEPTION_EXECUTE_HANDLER) {
        return true;
    }

    return false;
}

bool AntiDetection::CheckNtGlobalFlag() {
    // Check NtGlobalFlag in PEB
    __try {
        CUSTOM_PEB* peb = reinterpret_cast<CUSTOM_PEB*>(__readgsqword(0x60));
        return (peb->NtGlobalFlag & 0x70) != 0;
    }
    __except (EXCEPTION_EXECUTE_HANDLER) {
        return true;
    }
}

bool AntiDetection::CheckDebugPort() {
    // Simplified debug port check using IsDebuggerPresent
    return IsDebuggerPresent() != FALSE;
}

bool AntiDetection::CheckRemoteDebugger() {
    BOOL isRemoteDebuggerPresent = FALSE;
    CheckRemoteDebuggerPresent(GetCurrentProcess(), &isRemoteDebuggerPresent);
    return isRemoteDebuggerPresent != FALSE;
}

bool AntiDetection::CheckOutputDebugString() {
    // OutputDebugString technique
    SetLastError(0);
    OutputDebugStringA("Anti-Debug Test");
    return GetLastError() == 0;
}

bool AntiDetection::CheckCPUID() {
    // Check for VM signatures in CPUID
    int cpuInfo[4];
    __cpuid(cpuInfo, 0x40000000);

    // Check for hypervisor signatures
    char hypervisor[13] = {0};
    memcpy(hypervisor, &cpuInfo[1], 4);
    memcpy(hypervisor + 4, &cpuInfo[2], 4);
    memcpy(hypervisor + 8, &cpuInfo[3], 4);

    std::string hvString(hypervisor);
    return hvString.find("VMware") != std::string::npos ||
           hvString.find("VBoxVBox") != std::string::npos ||
           hvString.find("Microsoft Hv") != std::string::npos;
}

bool AntiDetection::CheckRegistry() {
    // Check for VM-related registry keys
    HKEY hKey;
    std::vector<std::string> vmKeys = {
        "HARDWARE\\DEVICEMAP\\Scsi\\Scsi Port 0\\Scsi Bus 0\\Target Id 0\\Logical Unit Id 0",
        "HARDWARE\\Description\\System",
        "SOFTWARE\\VMware, Inc.\\VMware Tools"
    };

    for (const auto& keyPath : vmKeys) {
        if (RegOpenKeyExA(HKEY_LOCAL_MACHINE, keyPath.c_str(), 0, KEY_READ, &hKey) == ERROR_SUCCESS) {
            RegCloseKey(hKey);
            return true;
        }
    }

    return false;
}

bool AntiDetection::CheckFiles() {
    // Check for VM-related files
    std::vector<std::string> vmFiles = {
        "C:\\windows\\system32\\drivers\\vmmouse.sys",
        "C:\\windows\\system32\\drivers\\vmhgfs.sys",
        "C:\\windows\\system32\\drivers\\VBoxMouse.sys",
        "C:\\windows\\system32\\drivers\\VBoxGuest.sys"
    };

    for (const auto& filePath : vmFiles) {
        if (GetFileAttributesA(filePath.c_str()) != INVALID_FILE_ATTRIBUTES) {
            return true;
        }
    }

    return false;
}

bool AntiDetection::CheckServices() {
    // Check for VM-related services
    SC_HANDLE scManager = OpenSCManagerA(nullptr, nullptr, SC_MANAGER_ENUMERATE_SERVICE);
    if (!scManager) return false;

    std::vector<std::string> vmServices = {
        "VMTools", "VMware Physical Disk Helper Service",
        "VBoxService", "VirtualBox Guest Additions Service"
    };

    for (const auto& serviceName : vmServices) {
        SC_HANDLE service = OpenServiceA(scManager, serviceName.c_str(), SERVICE_QUERY_STATUS);
        if (service) {
            CloseServiceHandle(service);
            CloseServiceHandle(scManager);
            return true;
        }
    }

    CloseServiceHandle(scManager);
    return false;
}

bool AntiDetection::CheckProcesses() {
    // Check for VM-related processes
    HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (snapshot == INVALID_HANDLE_VALUE) return false;

    PROCESSENTRY32 pe32;
    pe32.dwSize = sizeof(PROCESSENTRY32);

    std::vector<std::string> vmProcesses = {
        "vmtoolsd.exe", "vmwaretray.exe", "vmwareuser.exe",
        "VBoxService.exe", "VBoxTray.exe"
    };

    if (Process32First(snapshot, &pe32)) {
        do {
            std::string processName(pe32.szExeFile);
            for (const auto& vmProcess : vmProcesses) {
                if (processName.find(vmProcess) != std::string::npos) {
                    CloseHandle(snapshot);
                    return true;
                }
            }
        } while (Process32Next(snapshot, &pe32));
    }

    CloseHandle(snapshot);
    return false;
}

bool AntiDetection::CheckAnalysisTools() {
    // Check for analysis tools
    std::vector<std::string> analysisTools = {
        "ollydbg.exe", "x64dbg.exe", "windbg.exe", "ida.exe", "ida64.exe",
        "cheatengine.exe", "processhacker.exe", "procmon.exe", "wireshark.exe"
    };

    HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (snapshot == INVALID_HANDLE_VALUE) return false;

    PROCESSENTRY32 pe32;
    pe32.dwSize = sizeof(PROCESSENTRY32);

    if (Process32First(snapshot, &pe32)) {
        do {
            std::string processName(pe32.szExeFile);
            std::transform(processName.begin(), processName.end(), processName.begin(), ::tolower);

            for (const auto& tool : analysisTools) {
                if (processName.find(tool) != std::string::npos) {
                    CloseHandle(snapshot);
                    return true;
                }
            }
        } while (Process32Next(snapshot, &pe32));
    }

    CloseHandle(snapshot);
    return false;
}

bool AntiDetection::CheckMonitoringTools() {
    // Check for monitoring/hooking tools
    std::vector<std::string> monitoringTools = {
        "apimonitor.exe", "detours.dll", "easyhook.dll"
    };

    // Check processes
    HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (snapshot != INVALID_HANDLE_VALUE) {
        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);

        if (Process32First(snapshot, &pe32)) {
            do {
                std::string processName(pe32.szExeFile);
                std::transform(processName.begin(), processName.end(), processName.begin(), ::tolower);

                for (const auto& tool : monitoringTools) {
                    if (processName.find(tool) != std::string::npos) {
                        CloseHandle(snapshot);
                        return true;
                    }
                }
            } while (Process32Next(snapshot, &pe32));
        }
        CloseHandle(snapshot);
    }

    // Check loaded modules
    HANDLE process = GetCurrentProcess();
    HMODULE modules[1024];
    DWORD needed;

    if (EnumProcessModules(process, modules, sizeof(modules), &needed)) {
        for (unsigned int i = 0; i < (needed / sizeof(HMODULE)); i++) {
            char moduleName[MAX_PATH];
            if (GetModuleBaseNameA(process, modules[i], moduleName, sizeof(moduleName))) {
                std::string moduleStr(moduleName);
                std::transform(moduleStr.begin(), moduleStr.end(), moduleStr.begin(), ::tolower);

                for (const auto& tool : monitoringTools) {
                    if (moduleStr.find(tool) != std::string::npos) {
                        return true;
                    }
                }
            }
        }
    }

    return false;
}

bool AntiDetection::CheckForHooks() {
    // Basic hook detection - check if common APIs are hooked
    std::vector<std::pair<std::string, std::string>> apisToCheck = {
        {"kernel32.dll", "ReadProcessMemory"},
        {"kernel32.dll", "WriteProcessMemory"},
        {"ntdll.dll", "NtReadVirtualMemory"},
        {"ntdll.dll", "NtWriteVirtualMemory"}
    };

    for (const auto& [moduleName, apiName] : apisToCheck) {
        HMODULE module = GetModuleHandleA(moduleName.c_str());
        if (!module) continue;

        FARPROC apiAddress = GetProcAddress(module, apiName.c_str());
        if (!apiAddress) continue;

        // Check first few bytes for common hook patterns
        BYTE* bytes = reinterpret_cast<BYTE*>(apiAddress);

        // Check for JMP instruction (0xE9 or 0xEB)
        if (bytes[0] == 0xE9 || bytes[0] == 0xEB) {
            return true;
        }

        // Check for PUSH + RET pattern
        if (bytes[0] == 0x68 && bytes[5] == 0xC3) {
            return true;
        }
    }

    return false;
}

bool AntiDetection::CheckForInjectedDLLs() {
    HANDLE process = GetCurrentProcess();
    HMODULE modules[1024];
    DWORD needed;

    if (!EnumProcessModules(process, modules, sizeof(modules), &needed)) {
        return false;
    }

    for (unsigned int i = 0; i < (needed / sizeof(HMODULE)); i++) {
        char modulePath[MAX_PATH];
        if (GetModuleFileNameA(modules[i], modulePath, sizeof(modulePath))) {
            std::string moduleStr(modulePath);
            std::transform(moduleStr.begin(), moduleStr.end(), moduleStr.begin(), ::tolower);

            // Check if module is in trusted list
            bool isTrusted = false;
            for (const auto& trustedModule : m_trustedModules) {
                if (moduleStr.find(trustedModule) != std::string::npos) {
                    isTrusted = true;
                    break;
                }
            }

            if (!isTrusted) {
                // Check for suspicious DLL names/paths
                if (moduleStr.find("inject") != std::string::npos ||
                    moduleStr.find("hook") != std::string::npos ||
                    moduleStr.find("cheat") != std::string::npos ||
                    moduleStr.find("hack") != std::string::npos) {
                    return true;
                }
            }
        }
    }

    return false;
}

bool AntiDetection::CheckForSuspiciousProcesses() {
    return CheckAnalysisTools() || CheckMonitoringTools();
}

bool AntiDetection::ValidateModuleSignatures() {
    // In a real implementation, you would verify digital signatures
    // For now, just return true
    return true;
}

uint64_t AntiDetection::GetCurrentTimeMs() {
    auto now = std::chrono::steady_clock::now();
    auto duration = now.time_since_epoch();
    return std::chrono::duration_cast<std::chrono::milliseconds>(duration).count();
}
