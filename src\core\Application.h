#pragma once
#include "../memory/Memory.h"
#include "SkinChanger.h"
#include <memory>
#include <thread>
#include <atomic>

class Application {
public:
    Application();
    ~Application();

    // Lifecycle
    bool Initialize();
    void Run();
    void Shutdown();
    
    // State
    bool IsRunning() const { return m_running; }
    bool IsInitialized() const { return m_initialized; }
    
private:
    // Core components
    std::shared_ptr<Memory> m_memory;
    std::unique_ptr<SkinChanger> m_skinChanger;
    
    // State
    std::atomic<bool> m_running{false};
    std::atomic<bool> m_initialized{false};
    std::atomic<bool> m_showMenu{false};
    
    // Threads
    std::thread m_updateThread;
    std::thread m_inputThread;
    
    // Methods
    bool InitializeMemory();
    bool InitializeSkinChanger();
    void UpdateLoop();
    void InputLoop();
    void HandleInput();
    
    // Menu
    void ToggleMenu();
    void ShowMenu();
    void HideMenu();
    
    // Hotkeys
    void ProcessHotkeys();
    bool IsKeyPressed(int vKey);
    bool IsKeyDown(int vKey);
    
    // Utility
    void Sleep(int milliseconds);
};
