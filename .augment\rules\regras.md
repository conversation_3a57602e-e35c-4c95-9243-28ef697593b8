---
type: "always_apply"
---

# 🧠 IA Rulebook: Skin Changer Externo Funcional para CS2 (Counter-Strike 2)

> **Versão:** 2.0 — Ultra detalhada  
> **Tipo de Cheat:** Externo (Read/WriteMemory)  
> **Foco:** Skin changer local, funcional, modular, estável e discreto  
> **Detecção:** Evitar métodos detectáveis (como sendpacket spoof, write indiscriminado, etc.)

---

## 📌 SUMÁRIO

1. [🧬 Conceitos Fundamentais](#🧬-conceitos-fundamentais)  
2. [📦 Estrutura de Dados do CS2](#📦-estrutura-de-dados-do-cs2)  
3. [🔧 Offsets Necessários](#🔧-offsets-necessários)  
4. [🔁 Lógica de Substituição](#🔁-lógica-de-substituição)  
5. [🎭 Viewmodel e Worldmodel](#🎭-viewmodel-e-worldmodel)  
6. [⚠️ Forçando Atualização](#⚠️-forçando-atualização)  
7. [🧰 Tabela de Referência: IDs](#🧰-tabela-de-referência-ids)  
8. [🔒 Práticas Seguras e Antiban](#🔒-práticas-seguras-e-antiban)  
9. [🧪 Testes e Debug](#🧪-testes-e-debug)  
10. [🧠 Recomendações Avançadas](#🧠-recomendações-avançadas)

---

## 🧬 CONCEITOS FUNDAMENTAIS

A IA deve compreender que:

- O skin changer externo atua no client-side, alterando apenas a representação visual dos itens.
- Os atributos de uma skin são mantidos em entidades chamadas `CEconItemView`.
- Alterações são possíveis via manipulação direta de memória, escrevendo nos campos `fallback`, `paintkit`, etc.
- A engine usa estruturas com ponteiros encadeados, exigindo resolução correta de entidades.

---

## 📦 ESTRUTURA DE DADOS DO CS2

Fluxo de leitura até a arma:
dwEntityList → C_CSPlayerPawn → m_hMyWeapons[i] → C_EconEntity


Cada arma possui os seguintes campos a serem alterados:

- `m_iItemDefinitionIndex` → tipo da arma
- `m_iItemIDHigh` → se `-1`, ativa dados "fallback"
- `m_nFallbackPaintKit` → ID da skin
- `m_flFallbackWear` → desgaste (0.0001 = nova de fábrica)
- `m_nFallbackSeed` → padrão visual
- `m_nFallbackStatTrak` → valor do contador
- `m_szCustomName` → nome da arma

---

## 🔧 OFFSETS NECESSÁRIOS

| Descrição                    | Offset / Netvar                   |
|-----------------------------|-----------------------------------|
| EntityList                  | `dwEntityList`                    |
| LocalPlayerController       | `dwLocalPlayerController`         |
| LocalPawn                   | `dwLocalPlayerPawn`               |
| InventoryManager            | `dwInventoryManager`              |
| m_hMyWeapons                | `m_hMyWeapons` (array de 64)      |
| m_iItemDefinitionIndex      | `m_iItemDefinitionIndex`          |
| m_iItemIDHigh               | `m_iItemIDHigh`                   |
| m_nFallbackPaintKit         | `m_nFallbackPaintKit`             |
| m_flFallbackWear            | `m_flFallbackWear`                |
| m_nFallbackSeed             | `m_nFallbackSeed`                 |
| m_nFallbackStatTrak         | `m_nFallbackStatTrak`             |
| m_szCustomName              | `m_szCustomName`                  |
| m_hActiveWeapon             | `m_hActiveWeapon`                 |
| m_hViewModel                | `m_hViewModel`                    |
| m_iViewModelIndex           | `m_nModelIndex`                   |

---

## 🔁 LÓGICA DE SUBSTITUIÇÃO

Para cada arma:

1. Leia o handle de `m_hMyWeapons[i]`
2. Resolva o ponteiro para `C_EconEntity`
3. Valide o ponteiro
4. Escreva os seguintes dados:

```cpp
m_iItemIDHigh = -1;
m_nFallbackPaintKit = skinID;
m_flFallbackWear = 0.0001f;
m_nFallbackSeed = seed;
m_nFallbackStatTrak = valor;
m_szCustomName = "Minha Arma";

Para knives/gloves:

Alterar também m_iItemDefinitionIndex e m_nModelIndex

🎭 VIEWMODEL E WORLDMODEL
Leia m_hViewModel do player

Resolva o ponteiro para o ViewModel

Aplique:
viewModel->m_nModelIndex = GetModelIndexByWeaponID(weaponID);

A IA deve mapear os modelos usando modelprecache ou lista cacheada.

⚠️ FORÇANDO ATUALIZAÇÃO
Após alterações, a IA deve forçar atualização via:

CL_FullUpdate spoofado

Escrita em m_bClientSideAnimation ou m_bSpotted

Troca rápida de armas (m_hActiveWeapon)

Trigger interno de inventory_updated (se acessível)

🧰 TABELA DE REFERÊNCIA: IDs
Categoria	Nome	WeaponID	PaintKit	ModelIndex (string)
Knife	Karambit	507	38	models/weapons/karambit
Knife	Butterfly Knife	515	59	models/weapons/bfly
Knife	M9 Bayonet	508	416	models/weapons/m9
Rifle	AK-47	7	180	—
AWP	AWP	9	344	—
Glove	Sport Gloves	10006	10025	models/gloves/sport

🔒 PRÁTICAS SEGURAS E ANTIBAN
✅ Validar todos os ponteiros antes de escrever

✅ Usar leitura/escrita eficiente (loop assíncrono seguro)

✅ Encapsular toda lógica de troca em condição (ex: tecla ativadora)

🚫 Nunca altere armas de outros jogadores

🚫 Não use hooks nem DLLs

🚫 Não escreva em regiões não mapeadas

🚫 Não execute cl_fullupdate diretamente via comando de console externo (detectável)

🧪 TESTES E DEBUG
Teste	Esperado
Skin na AK-47	Visível no inventário e modelo
Troca para Karambit	Modelo visível na mão e worldmodel
Gloves aplicadas	Mãos com textura correta
StatTrak ativo	Valor mostrado (localmente)
Viewmodel alterado	ID visual corresponde à faca selecionada
Nenhum crash	Estável mesmo ao trocar de arma rapidamente

🧠 RECOMENDAÇÕES AVANÇADAS
🧩 Criar cache de modelos para leitura dinâmica via modelprecache

🧠 Fazer mapeamento automático de armas → skins via JSON externo

🛡️ Usar driver para leitura segura (bypass de handle protection)

📊 Criar interface GUI com preview (ex: ImGui externo)

📁 Modificar inventário completo simulando loadout (experimental)