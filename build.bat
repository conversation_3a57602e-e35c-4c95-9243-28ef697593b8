@echo off
echo ========================================
echo    CS2 Skin Changer - Build Script
echo ========================================
echo.

:: Check if CMake is installed
cmake --version >nul 2>&1
if errorlevel 1 (
    echo ERRO: CMake nao encontrado. Por favor, instale o CMake.
    echo Download: https://cmake.org/download/
    pause
    exit /b 1
)

:: Check if Visual Studio is available
where cl >nul 2>&1
if errorlevel 1 (
    echo Visual Studio compiler nao encontrado. Tentando configurar automaticamente...
    echo.

    :: Try to find and setup Visual Studio environment
    call :setup_vs_environment
    if errorlevel 1 (
        echo.
        echo ERRO: Nao foi possivel configurar o Visual Studio automaticamente.
        echo.
        echo SOLUCOES:
        echo 1. Execute setup_vs_environment.bat primeiro
        echo 2. Use um Developer Command Prompt
        echo 3. Instale Visual Studio com C++ tools
        echo.
        echo Download Visual Studio: https://visualstudio.microsoft.com/downloads/
        pause
        exit /b 1
    )
)

:: Get current directory
set "PROJECT_DIR=%~dp0"
echo Diretorio do projeto: %PROJECT_DIR%

:: Create build directory
if not exist "%PROJECT_DIR%build" mkdir "%PROJECT_DIR%build"
cd /d "%PROJECT_DIR%build"

echo Configurando projeto com CMake...
cmake .. -G "Visual Studio 17 2022" -A x64
if errorlevel 1 (
    echo ERRO: Falha na configuracao do CMake.
    cd /d "%PROJECT_DIR%"
    pause
    exit /b 1
)

echo.
echo Compilando projeto...
cmake --build . --config Release
if errorlevel 1 (
    echo ERRO: Falha na compilacao.
    cd /d "%PROJECT_DIR%"
    pause
    exit /b 1
)

cd /d "%PROJECT_DIR%"

echo.
echo ========================================
echo    Compilacao concluida com sucesso!
echo ========================================
echo.
echo Executavel criado em: build\bin\Release\CS2_SkinChanger.exe
echo.

:: Check if ImGui was downloaded
if not exist "external\imgui" (
    echo AVISO: ImGui nao foi encontrado.
    echo Execute download_imgui.bat para baixar as dependencias da GUI.
    echo.
)

echo Para executar o skin changer:
echo 1. Abra o CS2
echo 2. Execute build\bin\Release\CS2_SkinChanger.exe como Administrador
echo 3. Pressione F1 para abrir o menu (quando implementado)
echo.
pause
goto :eof

:setup_vs_environment
echo Procurando instalacoes do Visual Studio...

:: Check for Visual Studio 2022
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
    echo Encontrado: Visual Studio 2022 Community
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
    goto :check_compiler
)

if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" (
    echo Encontrado: Visual Studio 2022 Professional
    call "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat"
    goto :check_compiler
)

if exist "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat" (
    echo Encontrado: Visual Studio 2022 Enterprise
    call "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat"
    goto :check_compiler
)

:: Check for Visual Studio 2019
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" (
    echo Encontrado: Visual Studio 2019 Community
    call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"
    goto :check_compiler
)

if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat" (
    echo Encontrado: Visual Studio 2019 Professional
    call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat"
    goto :check_compiler
)

:: Check for Build Tools
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Auxiliary\Build\vcvars64.bat" (
    echo Encontrado: Visual Studio 2022 Build Tools
    call "C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Auxiliary\Build\vcvars64.bat"
    goto :check_compiler
)

if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Auxiliary\Build\vcvars64.bat" (
    echo Encontrado: Visual Studio 2019 Build Tools
    call "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Auxiliary\Build\vcvars64.bat"
    goto :check_compiler
)

echo Visual Studio nao encontrado em locais padrao.
exit /b 1

:check_compiler
where cl >nul 2>&1
if errorlevel 1 (
    echo Falha ao configurar compiler.
    exit /b 1
)
echo Compiler configurado com sucesso!
exit /b 0
