#include "Console.h"
#include <iostream>
#include <ctime>
#include <iomanip>
#include <sstream>

HANDLE Console::hConsole = nullptr;

void Console::Initialize() {
    AllocConsole();
    freopen_s((FILE**)stdout, "CONOUT$", "w", stdout);
    freopen_s((FILE**)stderr, "CONOUT$", "w", stderr);
    freopen_s((FILE**)stdin, "CONIN$", "r", stdin);
    
    hConsole = GetStdHandle(STD_OUTPUT_HANDLE);
    
    // Set console size
    COORD coord = { 120, 30 };
    SetConsoleScreenBufferSize(hConsole, coord);
    
    SMALL_RECT rect = { 0, 0, 119, 29 };
    SetConsoleWindowInfo(hConsole, TRUE, &rect);
}

void Console::SetTitle(const std::string& title) {
    SetConsoleTitleA(title.c_str());
}

void Console::Clear() {
    system("cls");
}

void Console::Pause() {
    std::cout << "\nPressione qualquer tecla para continuar...";
    std::cin.get();
}

std::string GetTimestamp() {
    auto now = std::time(nullptr);
    std::tm tm;
    localtime_s(&tm, &now);
    std::ostringstream oss;
    oss << std::put_time(&tm, "[%H:%M:%S]");
    return oss.str();
}

void Console::Info(const std::string& message) {
    SetColor(CYAN);
    std::cout << GetTimestamp() << " [INFO] ";
    SetColor(WHITE);
    std::cout << message << std::endl;
}

void Console::Success(const std::string& message) {
    SetColor(GREEN);
    std::cout << GetTimestamp() << " [SUCCESS] ";
    SetColor(WHITE);
    std::cout << message << std::endl;
}

void Console::Warning(const std::string& message) {
    SetColor(YELLOW);
    std::cout << GetTimestamp() << " [WARNING] ";
    SetColor(WHITE);
    std::cout << message << std::endl;
}

void Console::Error(const std::string& message) {
    SetColor(RED);
    std::cout << GetTimestamp() << " [ERROR] ";
    SetColor(WHITE);
    std::cout << message << std::endl;
}

void Console::Debug(const std::string& message) {
    SetColor(DARK_GRAY);
    std::cout << GetTimestamp() << " [DEBUG] ";
    SetColor(WHITE);
    std::cout << message << std::endl;
}

void Console::SetColor(Color color) {
    if (hConsole) {
        SetConsoleTextAttribute(hConsole, static_cast<WORD>(color));
    }
}

void Console::ResetColor() {
    SetColor(WHITE);
}
