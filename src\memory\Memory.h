#pragma once
#include "Process.h"
#include <memory>

class Memory {
public:
    Memory();
    ~Memory();

    // Process management
    bool AttachToCS2();
    bool IsAttached() const;
    void Detach();
    
    // Module bases
    uintptr_t GetClientBase() const { return m_clientBase; }
    uintptr_t GetEngine2Base() const { return m_engine2Base; }
    
    // Memory operations with validation
    template<typename T>
    T Read(uintptr_t address) {
        if (!IsValidAddress(address)) {
            return T{};
        }
        return m_process->Read<T>(address);
    }
    
    template<typename T>
    bool Write(uintptr_t address, const T& value) {
        if (!IsValidAddress(address)) {
            return false;
        }
        return m_process->Write<T>(address, value);
    }
    
    // Safe pointer dereferencing
    uintptr_t DerefPointer(uintptr_t address, const std::vector<uintptr_t>& offsets);
    uintptr_t ResolveAddress(uintptr_t base, const std::vector<uintptr_t>& offsets);
    
    // Entity operations
    uintptr_t GetEntityList() const;
    uintptr_t GetLocalPlayer() const;
    uintptr_t GetLocalPlayerPawn();
    uintptr_t GetEntity(int index);
    
    // Validation
    bool IsValidAddress(uintptr_t address) const;
    bool IsValidEntity(uintptr_t entity) const;
    bool IsValidPointer(uintptr_t pointer) const;
    
    // Utility
    std::string ReadString(uintptr_t address, size_t maxLength = 256);
    bool WriteString(uintptr_t address, const std::string& str);
    
    // Pattern scanning
    uintptr_t FindPattern(const std::string& moduleName, const std::string& pattern);
    
    // Get process instance
    Process* GetProcess() const { return m_process.get(); }
    
private:
    std::unique_ptr<Process> m_process;
    uintptr_t m_clientBase;
    uintptr_t m_engine2Base;
    
    bool InitializeModules();
    bool ValidateModules();
};
