@echo off
echo ========================================
echo   CS2 Skin Changer - Setup Checker
echo ========================================
echo.

set "errors=0"

echo Verificando requisitos do sistema...
echo.

:: Check Windows version
echo [1/8] Verificando versao do Windows...
ver | findstr /i "10\." >nul
if errorlevel 1 (
    ver | findstr /i "11\." >nul
    if errorlevel 1 (
        echo   ❌ ERRO: Windows 10/11 requerido
        set /a errors+=1
    ) else (
        echo   ✅ Windows 11 detectado
    )
) else (
    echo   ✅ Windows 10 detectado
)

:: Check if running as administrator
echo [2/8] Verificando privilegios de administrador...
net session >nul 2>&1
if errorlevel 1 (
    echo   ❌ ERRO: Execute como Administrador
    set /a errors+=1
) else (
    echo   ✅ Executando como Administrador
)

:: Check CMake
echo [3/8] Verificando CMake...
cmake --version >nul 2>&1
if errorlevel 1 (
    echo   ❌ ERRO: CMake nao encontrado
    echo      Download: https://cmake.org/download/
    set /a errors+=1
) else (
    echo   ✅ CMake encontrado
    cmake --version | findstr "cmake version"
)

:: Check Visual Studio compiler
echo [4/8] Verificando Visual Studio...
where cl >nul 2>&1
if errorlevel 1 (
    echo   ❌ ERRO: Visual Studio compiler nao encontrado
    echo.
    echo   SOLUCOES DISPONIVEIS:
    echo   1. Execute setup_vs_environment.bat
    echo   2. Execute install_build_tools.bat (como Admin)
    echo   3. Instale Visual Studio manualmente
    echo.
    set /a errors+=1
) else (
    echo   ✅ Visual Studio compiler encontrado
)

:: Check Git
echo [5/8] Verificando Git...
git --version >nul 2>&1
if errorlevel 1 (
    echo   ⚠️  AVISO: Git nao encontrado (opcional)
    echo      Necessario para baixar ImGui automaticamente
) else (
    echo   ✅ Git encontrado
)

:: Check project structure
echo [6/8] Verificando estrutura do projeto...
if not exist "src" (
    echo   ❌ ERRO: Diretorio src/ nao encontrado
    set /a errors+=1
) else (
    echo   ✅ Diretorio src/ encontrado
)

if not exist "CMakeLists.txt" (
    echo   ❌ ERRO: CMakeLists.txt nao encontrado
    set /a errors+=1
) else (
    echo   ✅ CMakeLists.txt encontrado
)

:: Check offsets
echo [7/8] Verificando offsets...
if not exist "output" (
    echo   ❌ ERRO: Diretorio output/ nao encontrado
    echo      Offsets do CS2 sao necessarios
    set /a errors+=1
) else (
    if not exist "output\client_dll.hpp" (
        echo   ❌ ERRO: Offsets do client.dll nao encontrados
        set /a errors+=1
    ) else (
        echo   ✅ Offsets encontrados
    )
)

:: Check ImGui
echo [8/8] Verificando ImGui...
if not exist "external\imgui" (
    echo   ⚠️  AVISO: ImGui nao encontrado
    echo      Execute download_imgui.bat para baixar
    echo      GUI nao funcionara sem ImGui
) else (
    echo   ✅ ImGui encontrado
)

echo.
echo ========================================

if %errors% equ 0 (
    echo   ✅ TUDO OK! Sistema pronto para compilacao
    echo.
    echo   PROXIMOS PASSOS:
    echo   1. Execute download_imgui.bat se nao tiver ImGui
    echo   2. Execute build.bat para compilar
    echo   3. Abra CS2 e execute como Administrador
    echo.
) else (
    echo   ❌ %errors% ERRO(S) ENCONTRADO(S)
    echo.
    echo   SCRIPTS DISPONIVEIS PARA CORRIGIR:
    echo   - setup_vs_environment.bat (configurar VS)
    echo   - install_build_tools.bat (instalar VS Build Tools)
    echo.
    echo   Corrija os erros e execute check_setup.bat novamente.
)

echo ========================================
echo.
pause
