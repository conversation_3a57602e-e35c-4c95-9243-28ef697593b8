#include "Process.h"
#include "../utils/Console.h"
#include <algorithm>
#include <sstream>

Process::Process() : m_handle(nullptr), m_processId(0) {}

Process::~Process() {
    Detach();
}

bool Process::Attach(const std::string& processName) {
    DWORD processId = GetProcessIdByName(processName);
    if (processId == 0) {
        Console::Error("Processo não encontrado: " + processName);
        return false;
    }
    
    return Attach(processId);
}

bool Process::Attach(DWORD processId) {
    if (m_handle) {
        Detach();
    }
    
    m_handle = OpenProcess(PROCESS_ALL_ACCESS, FALSE, processId);
    if (!m_handle) {
        Console::Error("Falha ao abrir processo. ID: " + std::to_string(processId));
        return false;
    }
    
    m_processId = processId;
    
    // Get process name
    HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (snapshot != INVALID_HANDLE_VALUE) {
        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);
        
        if (Process32First(snapshot, &pe32)) {
            do {
                if (pe32.th32ProcessID == processId) {
                    m_processName = pe32.szExeFile;
                    break;
                }
            } while (Process32Next(snapshot, &pe32));
        }
        CloseHandle(snapshot);
    }
    
    RefreshModules();
    Console::Success("Anexado ao processo: " + m_processName + " (PID: " + std::to_string(processId) + ")");
    return true;
}

void Process::Detach() {
    if (m_handle) {
        CloseHandle(m_handle);
        m_handle = nullptr;
    }
    m_processId = 0;
    m_processName.clear();
    m_modules.clear();
}

bool Process::IsValid() const {
    if (!m_handle) return false;
    
    DWORD exitCode;
    if (!GetExitCodeProcess(m_handle, &exitCode)) {
        return false;
    }
    
    return exitCode == STILL_ACTIVE;
}

bool Process::GetModuleInfo(const std::string& moduleName, ModuleInfo& info) {
    if (m_modules.empty()) {
        RefreshModules();
    }
    
    auto it = std::find_if(m_modules.begin(), m_modules.end(),
        [&moduleName](const ModuleInfo& mod) {
            return _stricmp(mod.name.c_str(), moduleName.c_str()) == 0;
        });
    
    if (it != m_modules.end()) {
        info = *it;
        return true;
    }
    
    return false;
}

std::vector<ModuleInfo> Process::GetModules() {
    RefreshModules();
    return m_modules;
}

uintptr_t Process::GetModuleBase(const std::string& moduleName) {
    ModuleInfo info;
    if (GetModuleInfo(moduleName, info)) {
        return info.base;
    }
    return 0;
}

size_t Process::GetModuleSize(const std::string& moduleName) {
    ModuleInfo info;
    if (GetModuleInfo(moduleName, info)) {
        return info.size;
    }
    return 0;
}

bool Process::ReadMemory(uintptr_t address, void* buffer, size_t size) {
    if (!IsValid()) return false;

    SIZE_T bytesRead;
    return ReadProcessMemory(m_handle, reinterpret_cast<LPCVOID>(address), buffer, size, &bytesRead) && bytesRead == size;
}

bool Process::WriteMemory(uintptr_t address, const void* buffer, size_t size) {
    if (!IsValid()) return false;

    SIZE_T bytesWritten;
    return WriteProcessMemory(m_handle, reinterpret_cast<LPVOID>(address), buffer, size, &bytesWritten) && bytesWritten == size;
}

std::string Process::ReadString(uintptr_t address, size_t maxLength) {
    std::vector<char> buffer(maxLength + 1, 0);
    if (ReadMemory(address, buffer.data(), maxLength)) {
        return std::string(buffer.data());
    }
    return "";
}

bool Process::WriteString(uintptr_t address, const std::string& str) {
    return WriteMemory(address, str.c_str(), str.length() + 1);
}

DWORD Process::GetProcessIdByName(const std::string& processName) {
    HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (snapshot == INVALID_HANDLE_VALUE) {
        return 0;
    }

    PROCESSENTRY32 pe32;
    pe32.dwSize = sizeof(PROCESSENTRY32);

    if (Process32First(snapshot, &pe32)) {
        do {
            if (_stricmp(pe32.szExeFile, processName.c_str()) == 0) {
                CloseHandle(snapshot);
                return pe32.th32ProcessID;
            }
        } while (Process32Next(snapshot, &pe32));
    }

    CloseHandle(snapshot);
    return 0;
}

std::vector<DWORD> Process::GetProcessIdsByName(const std::string& processName) {
    std::vector<DWORD> processIds;
    HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (snapshot == INVALID_HANDLE_VALUE) {
        return processIds;
    }

    PROCESSENTRY32 pe32;
    pe32.dwSize = sizeof(PROCESSENTRY32);

    if (Process32First(snapshot, &pe32)) {
        do {
            if (_stricmp(pe32.szExeFile, processName.c_str()) == 0) {
                processIds.push_back(pe32.th32ProcessID);
            }
        } while (Process32Next(snapshot, &pe32));
    }

    CloseHandle(snapshot);
    return processIds;
}

bool Process::IsProcessRunning(const std::string& processName) {
    return GetProcessIdByName(processName) != 0;
}

bool Process::RefreshModules() {
    m_modules.clear();

    HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPMODULE | TH32CS_SNAPMODULE32, m_processId);
    if (snapshot == INVALID_HANDLE_VALUE) {
        return false;
    }

    MODULEENTRY32 me32;
    me32.dwSize = sizeof(MODULEENTRY32);

    if (Module32First(snapshot, &me32)) {
        do {
            ModuleInfo info;
            info.name = me32.szModule;
            info.base = reinterpret_cast<uintptr_t>(me32.modBaseAddr);
            info.size = me32.modBaseSize;
            info.handle = me32.hModule;
            m_modules.push_back(info);
        } while (Module32Next(snapshot, &me32));
    }

    CloseHandle(snapshot);
    return true;
}

uintptr_t Process::PatternScan(const std::string& moduleName, const std::string& pattern) {
    ModuleInfo info;
    if (!GetModuleInfo(moduleName, info)) {
        return 0;
    }

    return PatternScan(info.base, info.size, pattern);
}

uintptr_t Process::PatternScan(uintptr_t start, size_t size, const std::string& pattern) {
    auto patternBytes = PatternToBytes(pattern);
    if (patternBytes.empty()) {
        return 0;
    }

    std::vector<uint8_t> buffer(size);
    if (!ReadMemory(start, buffer.data(), size)) {
        return 0;
    }

    for (size_t i = 0; i <= size - patternBytes.size(); ++i) {
        if (ComparePattern(buffer.data() + i, patternBytes)) {
            return start + i;
        }
    }

    return 0;
}

std::vector<uint8_t> Process::PatternToBytes(const std::string& pattern) {
    std::vector<uint8_t> bytes;
    std::istringstream iss(pattern);
    std::string token;

    while (iss >> token) {
        if (token == "?") {
            bytes.push_back(0x00); // Wildcard
        } else {
            bytes.push_back(static_cast<uint8_t>(std::stoul(token, nullptr, 16)));
        }
    }

    return bytes;
}

bool Process::ComparePattern(const uint8_t* data, const std::vector<uint8_t>& pattern) {
    for (size_t i = 0; i < pattern.size(); ++i) {
        if (pattern[i] != 0x00 && data[i] != pattern[i]) {
            return false;
        }
    }
    return true;
}
