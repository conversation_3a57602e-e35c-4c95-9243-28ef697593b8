#pragma once
#include "../core/SkinChanger.h"
#include <string>
#include <memory>
#include <map>
#include <vector>

// Simple JSON-like structure for configuration
struct JsonValue {
    enum Type { Null, Bool, Int, Float, String, Object, Array };
    
    Type type = Null;
    bool boolValue = false;
    int intValue = 0;
    float floatValue = 0.0f;
    std::string stringValue;
    std::map<std::string, std::shared_ptr<JsonValue>> objectValue;
    std::vector<std::shared_ptr<JsonValue>> arrayValue;
    
    JsonValue() = default;
    JsonValue(bool value) : type(Bool), boolValue(value) {}
    JsonValue(int value) : type(Int), intValue(value) {}
    JsonValue(float value) : type(Float), floatValue(value) {}
    JsonValue(const std::string& value) : type(String), stringValue(value) {}
    
    // Getters
    bool AsBool() const { return type == Bool ? boolValue : false; }
    int AsInt() const { return type == Int ? intValue : 0; }
    float AsFloat() const { return type == Float ? floatValue : 0.0f; }
    std::string AsString() const { return type == String ? stringValue : ""; }
    
    // Object access
    std::shared_ptr<JsonValue> Get(const std::string& key) const {
        if (type != Object) return nullptr;
        auto it = objectValue.find(key);
        return it != objectValue.end() ? it->second : nullptr;
    }
    
    void Set(const std::string& key, std::shared_ptr<JsonValue> value) {
        if (type != Object) {
            type = Object;
            objectValue.clear();
        }
        objectValue[key] = value;
    }
};

class ConfigManager {
public:
    ConfigManager();
    ~ConfigManager();

    // File operations
    bool LoadConfig(const std::string& filename = "config.json");
    bool SaveConfig(const std::string& filename = "config.json");
    bool ConfigExists(const std::string& filename = "config.json");
    
    // Skin changer integration
    void LoadSkinConfigs(std::shared_ptr<SkinChanger> skinChanger);
    void SaveSkinConfigs(std::shared_ptr<SkinChanger> skinChanger);
    
    // Configuration access
    void SetBool(const std::string& key, bool value);
    void SetInt(const std::string& key, int value);
    void SetFloat(const std::string& key, float value);
    void SetString(const std::string& key, const std::string& value);
    
    bool GetBool(const std::string& key, bool defaultValue = false);
    int GetInt(const std::string& key, int defaultValue = 0);
    float GetFloat(const std::string& key, float defaultValue = 0.0f);
    std::string GetString(const std::string& key, const std::string& defaultValue = "");
    
    // Weapon configuration
    void SetWeaponConfig(WeaponId weaponId, const SkinConfig& config);
    SkinConfig GetWeaponConfig(WeaponId weaponId);
    
    // Knife and glove configuration
    void SetKnifeConfig(const SkinConfig& config);
    void SetGloveConfig(const SkinConfig& config);
    SkinConfig GetKnifeConfig();
    SkinConfig GetGloveConfig();
    
    // Utility
    void CreateDefaultConfig();
    void ResetToDefaults();
    std::string GetConfigPath() const;
    
private:
    std::shared_ptr<JsonValue> m_root;
    std::string m_configPath;
    
    // JSON parsing/serialization
    std::shared_ptr<JsonValue> ParseJson(const std::string& jsonString);
    std::string SerializeJson(std::shared_ptr<JsonValue> value, int indent = 0);
    
    // Helper methods
    std::shared_ptr<JsonValue> GetOrCreateObject(const std::string& key);
    std::shared_ptr<JsonValue> SkinConfigToJson(const SkinConfig& config);
    SkinConfig JsonToSkinConfig(std::shared_ptr<JsonValue> json);
    
    // File I/O
    std::string ReadFile(const std::string& filename);
    bool WriteFile(const std::string& filename, const std::string& content);
    
    // Path utilities
    std::string GetExecutableDirectory();
    bool CreateDirectoryIfNotExists(const std::string& path);
};
