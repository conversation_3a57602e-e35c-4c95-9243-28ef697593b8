# CS2 Skin Changer v2.0 - Professional Edition

Um skin changer profissional e completo para Counter-Strike 2, desenvolvido com foco em estabilidade, segurança e funcionalidades avançadas.

## 🚀 Características

### ✨ Funcionalidades Principais
- **Skin Changer Completo**: Suporte para todas as armas, facas e luvas
- **StatTrak Support**: Adicione contadores StatTrak personalizados
- **Nomes Personalizados**: Defina nomes customizados para suas armas
- **Configuração de Desgaste**: Controle preciso do wear (desgaste) das skins
- **Seed Customizável**: Controle do padrão visual das skins
- **Interface Gráfica**: Menu ImGui intuitivo e profissional

### 🛡️ Segurança e Estabilidade
- **Sistema Anti-Detecção**: Múltiplas técnicas de evasão
- **Validação de Memória**: Verificações rigorosas antes de qualquer operação
- **Operações Seguras**: Leitura/escrita de memória com validação
- **Restauração Automática**: Restaura skins originais ao sair
- **Detecção de Debugger**: Proteção contra análise reversa

### ⚙️ Características Técnicas
- **Arquitetura Externa**: Não injeta código no processo do jogo
- **Multi-threaded**: Threads separadas para atualização e input
- **Sistema de Configuração**: Salva/carrega configurações em JSON
- **Logging Avançado**: Sistema de console com cores e timestamps
- **Otimização de Performance**: Atualizações eficientes com cache

## 📋 Requisitos

### Sistema
- Windows 10/11 (64-bit)
- Counter-Strike 2 instalado
- Privilégios de Administrador

### Desenvolvimento
- Visual Studio 2022 com C++ tools
- CMake 3.20 ou superior
- Git (para baixar dependências)

## 🔧 Instalação e Compilação

### 1. Clone o Repositório
```bash
git clone <repository-url>
cd skin-changer
```

### 2. Baixar Dependências
```bash
# Baixar ImGui para interface gráfica
download_imgui.bat
```

### 3. Verificar Requisitos
```bash
# Verificar se tudo está configurado
check_setup.bat
```

### 4. Configurar Visual Studio (se necessário)
```bash
# Configuração automática do VS
setup_vs_environment.bat

# OU instalar Build Tools automaticamente (como Admin)
install_build_tools.bat
```

### 5. Compilar o Projeto

```bash
# Compilação automática
build.bat
```

#### Manual (se necessário):
```bash
mkdir build
cd build
cmake .. -G "Visual Studio 17 2022" -A x64
cmake --build . --config Release
```

## 🎮 Como Usar

### 1. Preparação
1. Abra o Counter-Strike 2
2. Entre em um servidor ou modo offline
3. Execute `CS2_SkinChanger.exe` como **Administrador**

### 2. Controles
- **F1**: Abrir/Fechar menu (quando GUI estiver implementada)
- **F2**: Recarregar configurações
- **END**: Sair da aplicação

### 3. Configuração via Console
O skin changer vem com configurações padrão pré-definidas:
- **AK-47**: Redline com StatTrak
- **AWP**: Dragon Lore com StatTrak
- **Faca**: Karambit Doppler Phase 2
- **Luvas**: Sport Gloves Pandora's Box

## 📁 Estrutura do Projeto

```
skin-changer/
├── src/
│   ├── core/           # Lógica principal
│   │   ├── Application.cpp/h
│   │   └── SkinChanger.cpp/h
│   ├── memory/         # Sistema de memória
│   │   ├── Memory.cpp/h
│   │   └── Process.cpp/h
│   ├── data/           # Dados de armas e skins
│   │   └── WeaponData.cpp/h
│   ├── offsets/        # Offsets do CS2
│   │   └── Offsets.h
│   ├── security/       # Sistema anti-detecção
│   │   └── AntiDetection.cpp/h
│   ├── config/         # Gerenciamento de configuração
│   │   └── ConfigManager.cpp/h
│   ├── gui/            # Interface gráfica
│   │   └── GUI.cpp/h
│   └── utils/          # Utilitários
│       └── Console.cpp/h
├── output/             # Offsets atualizados do cs2-dumper
├── external/           # Dependências externas (ImGui)
├── build/              # Arquivos de compilação
├── CMakeLists.txt      # Configuração do CMake
├── build.bat           # Script de compilação
├── download_imgui.bat  # Download do ImGui
└── README.md           # Este arquivo
```

## ⚠️ Avisos Importantes

### Segurança
- **Use por sua própria conta e risco**
- **Pode resultar em ban VAC se detectado**
- **Recomendado apenas para uso offline ou servidores privados**
- **Não garantimos que seja 100% indetectável**

### Legalidade
- Este projeto é apenas para fins educacionais
- O uso em servidores oficiais da Valve pode violar os Termos de Serviço
- Os desenvolvedores não se responsabilizam pelo uso inadequado

## 🔄 Atualizações de Offsets

Os offsets do CS2 são atualizados regularmente. Para obter os mais recentes:

1. Visite: https://github.com/a2x/cs2-dumper
2. Baixe os offsets atualizados
3. Substitua os arquivos na pasta `output/`
4. Recompile o projeto

## 🐛 Solução de Problemas

### Erro: "Visual Studio compiler não encontrado"
- Execute `setup_vs_environment.bat`
- OU execute `install_build_tools.bat` como Administrador
- Veja o guia completo em `TROUBLESHOOTING.md`

### Erro: "CS2 não encontrado"
- Certifique-se de que o CS2 está rodando
- Execute como Administrador
- Verifique se o processo se chama "cs2.exe"

### Erro: "Falha ao anexar ao processo"
- Execute como Administrador
- Desative antivírus temporariamente
- Verifique se não há outros cheats rodando

### Erro: "Offsets desatualizados"
- Baixe offsets atualizados do cs2-dumper
- Recompile o projeto
- Verifique se a versão do CS2 é compatível

### Para mais problemas
Consulte o guia detalhado: **TROUBLESHOOTING.md**

## 🤝 Contribuição

Contribuições são bem-vindas! Por favor:

1. Faça um fork do projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Abra um Pull Request

## 📄 Licença

Este projeto é fornecido "como está" sem garantias. Use por sua própria conta e risco.

## 🙏 Créditos

- **cs2-dumper**: Por fornecer offsets atualizados
- **ImGui**: Interface gráfica
- **Valve**: Counter-Strike 2

---

**⚠️ DISCLAIMER: Este projeto é apenas para fins educacionais. O uso em servidores oficiais pode resultar em ban permanente.**
