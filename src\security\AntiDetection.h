#pragma once
#include <Windows.h>
#include <string>
#include <vector>
#include <memory>

class AntiDetection {
public:
    AntiDetection();
    ~AntiDetection();

    // Initialization
    bool Initialize();
    void Shutdown();
    
    // Detection checks
    bool IsDebuggerPresent();
    bool IsVirtualMachine();
    bool IsAnalysisEnvironment();
    bool IsSandbox();
    bool IsBeingAnalyzed();
    
    // Memory protection
    bool ValidateMemoryIntegrity();
    bool CheckForHooks();
    bool CheckForInjectedDLLs();
    
    // Process validation
    bool ValidateProcessIntegrity();
    bool CheckForSuspiciousProcesses();
    bool ValidateModuleSignatures();
    
    // Anti-tampering
    bool CheckFileIntegrity();
    bool ValidateExecutableChecksum();
    
    // Evasion techniques
    void RandomizeTimings();
    void ObfuscateMemoryAccess();
    void UseIndirectCalls();
    
    // Safe memory operations
    bool SafeReadMemory(HANDLE process, uintptr_t address, void* buffer, size_t size);
    bool SafeWriteMemory(HANDLE process, uintptr_t address, const void* buffer, size_t size);
    
    // Validation helpers
    bool ValidateAddress(uintptr_t address);
    bool ValidatePointer(void* pointer);
    bool ValidateHandle(HANDLE handle);
    
    // Stealth operations
    void SleepWithJitter(int baseMs, int jitterMs = 50);
    void RandomDelay();
    
    // Error handling
    void HandleDetection(const std::string& detectionType);
    void LogSecurityEvent(const std::string& event);
    
    // Settings
    void SetStealthMode(bool enabled) { m_stealthMode = enabled; }
    bool IsStealthMode() const { return m_stealthMode; }
    void SetValidationLevel(int level) { m_validationLevel = level; }
    int GetValidationLevel() const { return m_validationLevel; }
    
private:
    bool m_initialized = false;
    bool m_stealthMode = true;
    int m_validationLevel = 2; // 0=None, 1=Basic, 2=Normal, 3=Paranoid
    
    // Detection state
    bool m_debuggerDetected = false;
    bool m_vmDetected = false;
    bool m_analysisDetected = false;
    
    // Timing obfuscation
    uint64_t m_lastOperationTime = 0;
    std::vector<int> m_timingPattern;
    
    // Memory validation
    std::vector<uintptr_t> m_validatedAddresses;
    std::vector<std::string> m_trustedModules;
    
    // Detection methods
    bool CheckPEB();
    bool CheckTEB();
    bool CheckHeapFlags();
    bool CheckNtGlobalFlag();
    bool CheckDebugPort();
    bool CheckRemoteDebugger();
    bool CheckOutputDebugString();
    bool CheckUnhandledExceptionFilter();
    bool CheckSetUnhandledExceptionFilter();
    
    // VM detection
    bool CheckCPUID();
    bool CheckRegistry();
    bool CheckFiles();
    bool CheckServices();
    bool CheckProcesses();
    bool CheckMAC();
    bool CheckAdapter();
    bool CheckTemperature();
    
    // Analysis detection
    bool CheckAnalysisTools();
    bool CheckMonitoringTools();
    bool CheckNetworkMonitoring();
    bool CheckFileMonitoring();
    
    // Hook detection
    bool CheckAPIHooks();
    bool CheckInlineHooks();
    bool CheckIATHooks();
    bool CheckEATHooks();
    
    // Module validation
    bool ValidateModule(HMODULE module);
    bool CheckModuleSignature(const std::string& modulePath);
    
    // Utility
    uint64_t GetCurrentTimeMs();
    std::string GetRandomString(int length);
    void XorBuffer(void* buffer, size_t size, uint8_t key);
    
    // Obfuscation
    template<typename T>
    T ObfuscateValue(T value) {
        return value ^ static_cast<T>(0xDEADBEEF);
    }
    
    template<typename T>
    T DeobfuscateValue(T obfuscatedValue) {
        return obfuscatedValue ^ static_cast<T>(0xDEADBEEF);
    }
};
