#include "WeaponData.h"

std::unordered_map<WeaponId, WeaponInfo> WeaponData::s_weapons;
std::unordered_map<SkinId, std::string> WeaponData::s_skinNames;

const std::unordered_map<WeaponId, WeaponInfo>& WeaponData::GetWeaponMap() {
    if (s_weapons.empty()) {
        InitializeData();
    }
    return s_weapons;
}

const WeaponInfo* WeaponData::GetWeaponInfo(WeaponId id) {
    const auto& weapons = GetWeaponMap();
    auto it = weapons.find(id);
    return (it != weapons.end()) ? &it->second : nullptr;
}

std::vector<WeaponId> WeaponData::GetKnives() {
    std::vector<WeaponId> knives;
    for (const auto& pair : GetWeaponMap()) {
        if (pair.second.isKnife) {
            knives.push_back(pair.first);
        }
    }
    return knives;
}

std::vector<WeaponId> WeaponData::GetGloves() {
    std::vector<WeaponId> gloves;
    for (const auto& pair : GetWeaponMap()) {
        if (pair.second.isGlove) {
            gloves.push_back(pair.first);
        }
    }
    return gloves;
}

std::vector<WeaponId> WeaponData::GetWeapons(bool includeKnives, bool includeGloves) {
    std::vector<WeaponId> weapons;
    for (const auto& pair : GetWeaponMap()) {
        if ((pair.second.isKnife && !includeKnives) || (pair.second.isGlove && !includeGloves)) {
            continue;
        }
        weapons.push_back(pair.first);
    }
    return weapons;
}

std::string WeaponData::GetWeaponName(WeaponId id) {
    const auto* info = GetWeaponInfo(id);
    return info ? info->name : "Unknown";
}

std::string WeaponData::GetSkinName(SkinId id) {
    if (s_skinNames.empty()) {
        InitializeData();
    }
    
    auto it = s_skinNames.find(id);
    return (it != s_skinNames.end()) ? it->second : "Unknown";
}

void WeaponData::InitializeData() {
    // Initialize knives
    s_weapons[WeaponId::KNIFE_BAYONET] = {"Bayonet", WeaponId::KNIFE_BAYONET, "models/weapons/v_knife_bayonet.mdl", true, false, {}};
    s_weapons[WeaponId::KNIFE_FLIP] = {"Flip Knife", WeaponId::KNIFE_FLIP, "models/weapons/v_knife_flip.mdl", true, false, {}};
    s_weapons[WeaponId::KNIFE_GUT] = {"Gut Knife", WeaponId::KNIFE_GUT, "models/weapons/v_knife_gut.mdl", true, false, {}};
    s_weapons[WeaponId::KNIFE_KARAMBIT] = {"Karambit", WeaponId::KNIFE_KARAMBIT, "models/weapons/v_knife_karam.mdl", true, false, {}};
    s_weapons[WeaponId::KNIFE_M9_BAYONET] = {"M9 Bayonet", WeaponId::KNIFE_M9_BAYONET, "models/weapons/v_knife_m9_bay.mdl", true, false, {}};
    s_weapons[WeaponId::KNIFE_HUNTSMAN] = {"Huntsman Knife", WeaponId::KNIFE_HUNTSMAN, "models/weapons/v_knife_tactical.mdl", true, false, {}};
    s_weapons[WeaponId::KNIFE_FALCHION] = {"Falchion Knife", WeaponId::KNIFE_FALCHION, "models/weapons/v_knife_falchion_advanced.mdl", true, false, {}};
    s_weapons[WeaponId::KNIFE_BOWIE] = {"Bowie Knife", WeaponId::KNIFE_BOWIE, "models/weapons/v_knife_survival_bowie.mdl", true, false, {}};
    s_weapons[WeaponId::KNIFE_BUTTERFLY] = {"Butterfly Knife", WeaponId::KNIFE_BUTTERFLY, "models/weapons/v_knife_butterfly.mdl", true, false, {}};
    s_weapons[WeaponId::KNIFE_SHADOW_DAGGERS] = {"Shadow Daggers", WeaponId::KNIFE_SHADOW_DAGGERS, "models/weapons/v_knife_push.mdl", true, false, {}};
    s_weapons[WeaponId::KNIFE_URSUS] = {"Ursus Knife", WeaponId::KNIFE_URSUS, "models/weapons/v_knife_ursus.mdl", true, false, {}};
    s_weapons[WeaponId::KNIFE_NAVAJA] = {"Navaja Knife", WeaponId::KNIFE_NAVAJA, "models/weapons/v_knife_gypsy_jackknife.mdl", true, false, {}};
    s_weapons[WeaponId::KNIFE_STILETTO] = {"Stiletto Knife", WeaponId::KNIFE_STILETTO, "models/weapons/v_knife_stiletto.mdl", true, false, {}};
    s_weapons[WeaponId::KNIFE_TALON] = {"Talon Knife", WeaponId::KNIFE_TALON, "models/weapons/v_knife_widowmaker.mdl", true, false, {}};
    s_weapons[WeaponId::KNIFE_SKELETON] = {"Skeleton Knife", WeaponId::KNIFE_SKELETON, "models/weapons/v_knife_skeleton.mdl", true, false, {}};
    
    // Initialize gloves
    s_weapons[WeaponId::GLOVE_STUDDED_BLOODHOUND] = {"Bloodhound Gloves", WeaponId::GLOVE_STUDDED_BLOODHOUND, "models/weapons/v_models/arms/glove_bloodhound/v_glove_bloodhound.mdl", false, true, {}};
    s_weapons[WeaponId::GLOVE_SPORTY] = {"Sport Gloves", WeaponId::GLOVE_SPORTY, "models/weapons/v_models/arms/glove_sporty/v_glove_sporty.mdl", false, true, {}};
    s_weapons[WeaponId::GLOVE_SLICK] = {"Driver Gloves", WeaponId::GLOVE_SLICK, "models/weapons/v_models/arms/glove_slick/v_glove_slick.mdl", false, true, {}};
    s_weapons[WeaponId::GLOVE_LEATHER_WRAP] = {"Hand Wraps", WeaponId::GLOVE_LEATHER_WRAP, "models/weapons/v_models/arms/glove_handwrap_leathery/v_glove_handwrap_leathery.mdl", false, true, {}};
    s_weapons[WeaponId::GLOVE_MOTORCYCLE] = {"Moto Gloves", WeaponId::GLOVE_MOTORCYCLE, "models/weapons/v_models/arms/glove_motorcycle/v_glove_motorcycle.mdl", false, true, {}};
    s_weapons[WeaponId::GLOVE_SPECIALIST] = {"Specialist Gloves", WeaponId::GLOVE_SPECIALIST, "models/weapons/v_models/arms/glove_specialist/v_glove_specialist.mdl", false, true, {}};
    s_weapons[WeaponId::GLOVE_HYDRA] = {"Hydra Gloves", WeaponId::GLOVE_HYDRA, "models/weapons/v_models/arms/glove_bloodhound/v_glove_bloodhound_hydra.mdl", false, true, {}};
    
    // Initialize weapons
    s_weapons[WeaponId::AK47] = {"AK-47", WeaponId::AK47, "", false, false, {}};
    s_weapons[WeaponId::M4A4] = {"M4A4", WeaponId::M4A4, "", false, false, {}};
    s_weapons[WeaponId::M4A1_S] = {"M4A1-S", WeaponId::M4A1_S, "", false, false, {}};
    s_weapons[WeaponId::AWP] = {"AWP", WeaponId::AWP, "", false, false, {}};
    s_weapons[WeaponId::GLOCK] = {"Glock-18", WeaponId::GLOCK, "", false, false, {}};
    s_weapons[WeaponId::USP_S] = {"USP-S", WeaponId::USP_S, "", false, false, {}};
    s_weapons[WeaponId::DEAGLE] = {"Desert Eagle", WeaponId::DEAGLE, "", false, false, {}};
    
    // Initialize skin names
    s_skinNames[SkinId::AK47_REDLINE] = "Redline";
    s_skinNames[SkinId::AK47_VULCAN] = "Vulcan";
    s_skinNames[SkinId::AK47_FIRE_SERPENT] = "Fire Serpent";
    s_skinNames[SkinId::AK47_BLOODSPORT] = "Bloodsport";
    s_skinNames[SkinId::AK47_NEON_RIDER] = "Neon Rider";
    s_skinNames[SkinId::AK47_FUEL_INJECTOR] = "Fuel Injector";
    s_skinNames[SkinId::AK47_ASIIMOV] = "Asiimov";
    s_skinNames[SkinId::AK47_WASTELAND_REBEL] = "Wasteland Rebel";
    s_skinNames[SkinId::AK47_JAGUAR] = "Jaguar";
    s_skinNames[SkinId::AK47_CASE_HARDENED] = "Case Hardened";
    
    s_skinNames[SkinId::AWP_DRAGON_LORE] = "Dragon Lore";
    s_skinNames[SkinId::AWP_MEDUSA] = "Medusa";
    s_skinNames[SkinId::AWP_ASIIMOV] = "Asiimov";
    s_skinNames[SkinId::AWP_LIGHTNING_STRIKE] = "Lightning Strike";
    s_skinNames[SkinId::AWP_HYPER_BEAST] = "Hyper Beast";
    s_skinNames[SkinId::AWP_REDLINE] = "Redline";
    s_skinNames[SkinId::AWP_BOOM] = "BOOM";
    s_skinNames[SkinId::AWP_GRAPHITE] = "Graphite";
    s_skinNames[SkinId::AWP_FEVER_DREAM] = "Fever Dream";
    s_skinNames[SkinId::AWP_NEO_NOIR] = "Neo-Noir";
    
    s_skinNames[SkinId::KNIFE_DOPPLER_PHASE1] = "Doppler Phase 1";
    s_skinNames[SkinId::KNIFE_DOPPLER_PHASE2] = "Doppler Phase 2";
    s_skinNames[SkinId::KNIFE_DOPPLER_PHASE3] = "Doppler Phase 3";
    s_skinNames[SkinId::KNIFE_DOPPLER_PHASE4] = "Doppler Phase 4";
    s_skinNames[SkinId::KNIFE_FADE] = "Fade";
    s_skinNames[SkinId::KNIFE_TIGER_TOOTH] = "Tiger Tooth";
    s_skinNames[SkinId::KNIFE_MARBLE_FADE] = "Marble Fade";
    s_skinNames[SkinId::KNIFE_DAMASCUS_STEEL] = "Damascus Steel";
    s_skinNames[SkinId::KNIFE_CRIMSON_WEB] = "Crimson Web";
    s_skinNames[SkinId::KNIFE_CASE_HARDENED] = "Case Hardened";
    s_skinNames[SkinId::KNIFE_SLAUGHTER] = "Slaughter";
    s_skinNames[SkinId::KNIFE_VANILLA] = "Vanilla";
    
    s_skinNames[SkinId::GLOVE_CRIMSON_KIMONO] = "Crimson Kimono";
    s_skinNames[SkinId::GLOVE_FADE] = "Fade";
    s_skinNames[SkinId::GLOVE_FOUNDATION] = "Foundation";
    s_skinNames[SkinId::GLOVE_BADLANDS] = "Badlands";
    s_skinNames[SkinId::GLOVE_PANDORAS_BOX] = "Pandora's Box";
    s_skinNames[SkinId::GLOVE_COOL_MINT] = "Cool Mint";
    s_skinNames[SkinId::GLOVE_FOREST_DDPAT] = "Forest DDPAT";
    s_skinNames[SkinId::GLOVE_CRIMSON_WEAVE] = "Crimson Weave";
    s_skinNames[SkinId::GLOVE_LEATHER_WRAP] = "Leather";
    s_skinNames[SkinId::GLOVE_VICE] = "Vice";
    s_skinNames[SkinId::GLOVE_SUPERCONDUCTOR] = "Superconductor";
    s_skinNames[SkinId::GLOVE_ARID] = "Arid";
    s_skinNames[SkinId::GLOVE_OVERTAKE] = "Overtake";
    s_skinNames[SkinId::GLOVE_RACING_GREEN] = "Racing Green";
    s_skinNames[SkinId::GLOVE_AMPHIBIOUS] = "Amphibious";
    s_skinNames[SkinId::GLOVE_BRONZE_MORPH] = "Bronze Morph";
}
