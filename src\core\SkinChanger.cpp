#include "../common.h"
#include "SkinChanger.h"
#include "../utils/Console.h"
#include "../offsets/Offsets.h"

SkinChanger::SkinChanger() {
    // Initialize default knife config
    m_knifeConfig.weaponId = WeaponId::KNIFE_KARAMBIT;
    m_knifeConfig.skinId = SkinId::KNIFE_DOPPLER_PHASE2;
    m_knifeConfig.seed = 661;
    m_knifeConfig.wear = 0.0001f;
    m_knifeConfig.statTrak = -1;
    m_knifeConfig.customName = "";
    m_knifeConfig.enabled = false;
    
    // Initialize default glove config
    m_gloveConfig.weaponId = WeaponId::GLOVE_SPORTY;
    m_gloveConfig.skinId = SkinId::GLOVE_PANDORAS_BOX;
    m_gloveConfig.seed = 0;
    m_gloveConfig.wear = 0.0001f;
    m_gloveConfig.statTrak = -1;
    m_gloveConfig.customName = "";
    m_gloveConfig.enabled = false;
}

SkinChanger::~SkinChanger() {
    Shutdown();
}

bool SkinChanger::Initialize(std::shared_ptr<Memory> memory) {
    if (!memory || !memory->IsAttached()) {
        LogError("Memória inválida ou não anexada");
        return false;
    }
    
    m_memory = memory;
    
    if (!ValidateMemoryAccess()) {
        LogError("Falha na validação de acesso à memória");
        return false;
    }
    
    m_initialized = true;
    LogInfo("SkinChanger inicializado com sucesso");
    return true;
}

void SkinChanger::Shutdown() {
    if (m_initialized) {
        RestoreOriginalSkins();
        m_initialized = false;
        m_memory.reset();
        LogInfo("SkinChanger finalizado");
    }
}

void SkinChanger::Update() {
    if (!m_initialized || !m_globalEnabled) {
        return;
    }
    
    uint64_t currentTime = GetCurrentTimeMs();
    if (currentTime - m_lastUpdateTime < m_updateInterval) {
        return;
    }
    
    m_lastUpdateTime = currentTime;
    
    try {
        if (UpdatePlayerWeapons()) {
            ApplySkins();
        }
    } catch (const std::exception& e) {
        LogError("Erro durante atualização: " + std::string(e.what()));
    }
}

void SkinChanger::ApplySkins() {
    if (!m_initialized || m_cachedWeapons.empty()) {
        return;
    }

    uintptr_t playerPawn = m_memory->GetLocalPlayerPawn();
    if (!ValidatePlayerPawn(playerPawn)) {
        return;
    }

    static bool firstApplication = true;
    if (firstApplication) {
        LogInfo("🎨 Aplicando skins para " + std::to_string(m_cachedWeapons.size()) + " armas encontradas!");
        firstApplication = false;
    }

    int skinsApplied = 0;

    // Apply gloves first
    if (m_gloveEnabled && m_gloveConfig.enabled) {
        LogInfo("🧤 Aplicando luvas: " + WeaponData::GetWeaponName(m_gloveConfig.weaponId));
        SetGloveSkin(playerPawn, m_gloveConfig);
        skinsApplied++;
    }

    // Apply weapon skins
    for (const auto& weapon : m_cachedWeapons) {
        if (!weapon.isValid) continue;

        int weaponIdInt = static_cast<int>(weapon.weaponId);
        std::string weaponName = WeaponData::GetWeaponName(weapon.weaponId);

        // Check if it's a knife
        if (m_knifeEnabled && m_knifeConfig.enabled &&
            (weaponIdInt >= 500 && weaponIdInt <= 525)) {
            LogInfo("🔪 Aplicando faca: " + WeaponData::GetWeaponName(m_knifeConfig.weaponId) +
                   " | " + WeaponData::GetSkinName(m_knifeConfig.skinId));
            SetKnifeSkin(weapon.address, m_knifeConfig);
            skinsApplied++;
            continue;
        }

        // Check for weapon-specific config
        auto it = m_skinConfigs.find(weapon.weaponId);
        if (it != m_skinConfigs.end() && it->second.enabled) {
            LogInfo("🔫 Aplicando skin: " + weaponName + " | " + WeaponData::GetSkinName(it->second.skinId) +
                   " (Wear: " + std::to_string(it->second.wear) + ")");
            ApplySkinToWeapon(weapon, it->second);
            skinsApplied++;
        }
    }

    if (skinsApplied > 0) {
        LogInfo("✅ " + std::to_string(skinsApplied) + " skins aplicadas com sucesso!");
    }
}

void SkinChanger::RestoreOriginalSkins() {
    if (!m_initialized) {
        return;
    }
    
    for (const auto& weapon : m_cachedWeapons) {
        if (weapon.isValid) {
            RestoreWeaponSkin(weapon);
        }
    }
    
    LogInfo("Skins originais restauradas");
}

void SkinChanger::SetSkinConfig(WeaponId weaponId, const SkinConfig& config) {
    m_skinConfigs[weaponId] = config;
}

SkinConfig SkinChanger::GetSkinConfig(WeaponId weaponId) const {
    auto it = m_skinConfigs.find(weaponId);
    if (it != m_skinConfigs.end()) {
        return it->second;
    }
    
    // Return default config
    SkinConfig defaultConfig;
    defaultConfig.weaponId = weaponId;
    return defaultConfig;
}

void SkinChanger::EnableSkin(WeaponId weaponId, bool enabled) {
    auto it = m_skinConfigs.find(weaponId);
    if (it != m_skinConfigs.end()) {
        it->second.enabled = enabled;
    } else {
        SkinConfig config;
        config.weaponId = weaponId;
        config.enabled = enabled;
        m_skinConfigs[weaponId] = config;
    }
}

bool SkinChanger::IsSkinEnabled(WeaponId weaponId) const {
    auto it = m_skinConfigs.find(weaponId);
    return (it != m_skinConfigs.end()) ? it->second.enabled : false;
}

void SkinChanger::SetKnife(WeaponId knifeId, SkinId skinId) {
    m_knifeConfig.weaponId = knifeId;
    m_knifeConfig.skinId = skinId;
    m_knifeConfig.enabled = true;
    m_knifeEnabled = true;
}

void SkinChanger::SetGloves(WeaponId gloveId, SkinId skinId) {
    m_gloveConfig.weaponId = gloveId;
    m_gloveConfig.skinId = skinId;
    m_gloveConfig.enabled = true;
    m_gloveEnabled = true;
}

void SkinChanger::DisableKnife() {
    m_knifeEnabled = false;
    m_knifeConfig.enabled = false;
}

void SkinChanger::DisableGloves() {
    m_gloveEnabled = false;
    m_gloveConfig.enabled = false;
}

void SkinChanger::ForceUpdate() {
    m_lastUpdateTime = 0;
    m_lastPlayerPawn = 0;
    m_cachedWeapons.clear();
    Update();
}

void SkinChanger::ReloadConfigs() {
    // This would load from config file in a real implementation
    LogInfo("Configurações recarregadas");
}

std::vector<WeaponEntity> SkinChanger::GetPlayerWeapons() {
    return m_cachedWeapons;
}

bool SkinChanger::UpdatePlayerWeapons() {
    uintptr_t playerPawn = m_memory->GetLocalPlayerPawn();

    static uint64_t lastDebugTime = 0;
    uint64_t currentTime = GetCurrentTimeMs();

    // Debug the pawn retrieval process
    static uint64_t lastPawnDebug = 0;
    if (currentTime - lastPawnDebug > 10000) {
        uintptr_t clientBase = m_memory->GetClientBase();
        uintptr_t directPawn = m_memory->Read<uintptr_t>(clientBase + 0x18580D0); // dwLocalPlayerPawn
        uintptr_t localController = m_memory->Read<uintptr_t>(clientBase + 0x1A52D20); // dwLocalPlayerController

        LogInfo("🔍 Pawn Debug:");
        LogInfo("  ClientBase: 0x" + std::to_string(clientBase));
        LogInfo("  DirectPawn: 0x" + std::to_string(directPawn));
        LogInfo("  LocalController: 0x" + std::to_string(localController));
        LogInfo("  FinalPawn: 0x" + std::to_string(playerPawn));
        lastPawnDebug = currentTime;
    }

    if (!playerPawn) {
        if (currentTime - lastDebugTime > 5000) {
            LogError("PlayerPawn é NULL - player não está em jogo?");
            lastDebugTime = currentTime;
        }
        return false;
    }

    // Check player state with multiple methods
    int health = m_memory->Read<int>(playerPawn + 0x344); // m_iHealth (correct offset)
    uint8_t lifeState = m_memory->Read<uint8_t>(playerPawn + 0x348); // m_lifeState
    uint8_t teamNum = m_memory->Read<uint8_t>(playerPawn + 0x3E3); // m_iTeamNum

    static bool wasAlive = false;
    static bool firstSpawn = true;
    static uint64_t lastDetailedLog = 0;

    // More detailed logging every 10 seconds
    if (currentTime - lastDetailedLog > 10000) {
        LogInfo("🔍 Player Status Debug:");
        LogInfo("  Health: " + std::to_string(health));
        LogInfo("  LifeState: " + std::to_string(lifeState));
        LogInfo("  Team: " + std::to_string(teamNum));
        LogInfo("  PlayerPawn: 0x" + std::to_string(playerPawn));
        lastDetailedLog = currentTime;
    }

    // Check if player is alive and in game
    bool isAlive = (health > 0 && lifeState == 0 && teamNum > 0);

    if (!isAlive) {
        if (wasAlive) {
            LogInfo("💀 Player morreu - aguardando respawn...");
            wasAlive = false;
        } else if (currentTime - lastDebugTime > 15000) {
            if (health <= 0) {
                LogInfo("⏳ Aguardando player spawnar... (Health: " + std::to_string(health) + ")");
            } else if (teamNum <= 0) {
                LogInfo("⏳ Aguardando player entrar em um time... (Team: " + std::to_string(teamNum) + ")");
            } else if (lifeState != 0) {
                LogInfo("⏳ Player não está vivo... (LifeState: " + std::to_string(lifeState) + ")");
            }
            LogInfo("💡 Certifique-se de estar em um servidor e ter spawnado!");
            lastDebugTime = currentTime;
        }
        return false;
    } else if (!wasAlive) {
        LogInfo("🎮 Player spawnou! Aplicando skins... (Health: " + std::to_string(health) + ", Team: " + std::to_string(teamNum) + ")");
        wasAlive = true;
        firstSpawn = false;
        // Force immediate skin application
        m_lastUpdateTime = 0;
    }

    if (!ValidatePlayerPawn(playerPawn)) {
        if (currentTime - lastDebugTime > 5000) {
            LogError("PlayerPawn inválido: 0x" + std::to_string(playerPawn));
            lastDebugTime = currentTime;
        }
        return false;
    }

    // Check if player changed
    if (playerPawn != m_lastPlayerPawn) {
        m_lastPlayerPawn = playerPawn;
        m_cachedWeapons.clear();
        LogInfo("Novo player detectado: 0x" + std::to_string(playerPawn));
    }

    // Get weapon services
    uintptr_t weaponServicesOffset = playerPawn + Offsets::BasePlayerPawn::m_pWeaponServices;
    uintptr_t weaponServices = m_memory->Read<uintptr_t>(weaponServicesOffset);

    static uint64_t lastDebugTime2 = 0;
    if (currentTime - lastDebugTime2 > 5000) { // Debug info every 5 seconds
        LogInfo("PlayerPawn: 0x" + std::to_string(playerPawn));
        LogInfo("WeaponServices offset: 0x" + std::to_string(weaponServicesOffset));
        LogInfo("WeaponServices pointer: 0x" + std::to_string(weaponServices));
        lastDebugTime2 = currentTime;
    }

    if (!weaponServices) {
        static uint64_t lastErrorTime2 = 0;
        if (currentTime - lastErrorTime2 > 5000) { // Error only every 5 seconds
            LogError("WeaponServices inválido");
            lastErrorTime2 = currentTime;
        }
        return false;
    }

    // Get weapons array
    uintptr_t weaponsArray = weaponServices + Offsets::WeaponServices::m_hMyWeapons;

    m_cachedWeapons.clear();
    int weaponsFound = 0;

    // Iterate through weapons (max 64)
    for (int i = 0; i < 64; ++i) {
        uint32_t weaponHandle = m_memory->Read<uint32_t>(weaponsArray + (i * 4));
        if (!weaponHandle) continue;

        // Resolve weapon entity
        uintptr_t weaponEntity = ResolveEntityHandle(weaponHandle);
        if (!weaponEntity || !IsValidWeapon(weaponEntity)) continue;

        WeaponEntity weapon = GetWeaponEntity(weaponEntity);
        if (weapon.isValid) {
            m_cachedWeapons.push_back(weapon);
            weaponsFound++;
            LogInfo("Arma encontrada: ID=" + std::to_string(static_cast<int>(weapon.weaponId)) +
                   " Endereço=0x" + std::to_string(weapon.address));
        }
    }

    if (weaponsFound > 0) {
        static int lastWeaponCount = 0;
        if (weaponsFound != lastWeaponCount) {
            LogInfo("🔍 " + std::to_string(weaponsFound) + " armas encontradas no inventário!");
            lastWeaponCount = weaponsFound;
        }
    }

    return !m_cachedWeapons.empty();
}

bool SkinChanger::ApplySkinToWeapon(const WeaponEntity& weapon, const SkinConfig& config) {
    if (!weapon.isValid || !weapon.address) {
        return false;
    }

    return SetWeaponSkin(weapon.address, config);
}

bool SkinChanger::RestoreWeaponSkin(const WeaponEntity& weapon) {
    if (!weapon.isValid || !weapon.address) {
        return false;
    }

    // Restore original values
    bool success = true;

    success &= m_memory->Write<int>(weapon.address + Offsets::EconItemView::m_iItemDefinitionIndex, weapon.originalItemDefinitionIndex);
    success &= m_memory->Write<int>(weapon.address + Offsets::EconItemView::m_iItemIDHigh, weapon.originalItemIDHigh);
    success &= m_memory->Write<int>(weapon.address + Offsets::EconEntity::m_nFallbackPaintKit, weapon.originalPaintKit);
    success &= m_memory->Write<float>(weapon.address + Offsets::EconEntity::m_flFallbackWear, weapon.originalWear);
    success &= m_memory->Write<int>(weapon.address + Offsets::EconEntity::m_nFallbackSeed, weapon.originalSeed);
    success &= m_memory->Write<int>(weapon.address + Offsets::EconEntity::m_nFallbackStatTrak, weapon.originalStatTrak);

    if (!weapon.originalCustomName.empty()) {
        m_memory->WriteString(weapon.address + Offsets::EconItemView::m_szCustomName, weapon.originalCustomName);
    }

    return success;
}

WeaponEntity SkinChanger::GetWeaponEntity(uintptr_t weaponAddress) {
    WeaponEntity weapon;
    weapon.address = weaponAddress;
    weapon.weaponId = GetWeaponId(weaponAddress);
    weapon.isValid = IsValidWeapon(weaponAddress);

    if (weapon.isValid) {
        // Store original values
        weapon.originalItemDefinitionIndex = m_memory->Read<int>(weaponAddress + Offsets::EconItemView::m_iItemDefinitionIndex);
        weapon.originalItemIDHigh = m_memory->Read<int>(weaponAddress + Offsets::EconItemView::m_iItemIDHigh);
        weapon.originalPaintKit = m_memory->Read<int>(weaponAddress + Offsets::EconEntity::m_nFallbackPaintKit);
        weapon.originalWear = m_memory->Read<float>(weaponAddress + Offsets::EconEntity::m_flFallbackWear);
        weapon.originalSeed = m_memory->Read<int>(weaponAddress + Offsets::EconEntity::m_nFallbackSeed);
        weapon.originalStatTrak = m_memory->Read<int>(weaponAddress + Offsets::EconEntity::m_nFallbackStatTrak);
        weapon.originalCustomName = m_memory->ReadString(weaponAddress + Offsets::EconItemView::m_szCustomName);
    }

    return weapon;
}

bool SkinChanger::IsValidWeapon(uintptr_t weaponAddress) {
    if (!m_memory->IsValidAddress(weaponAddress)) {
        return false;
    }

    // Check if it's a valid weapon by reading item definition index
    int itemDefIndex = m_memory->Read<int>(weaponAddress + Offsets::EconItemView::m_iItemDefinitionIndex);
    return itemDefIndex > 0 && itemDefIndex < 10000; // Reasonable range for weapon IDs
}

WeaponId SkinChanger::GetWeaponId(uintptr_t weaponAddress) {
    int itemDefIndex = m_memory->Read<int>(weaponAddress + Offsets::EconItemView::m_iItemDefinitionIndex);
    return static_cast<WeaponId>(itemDefIndex);
}

bool SkinChanger::SetWeaponSkin(uintptr_t weaponAddress, const SkinConfig& config) {
    if (!weaponAddress || !m_memory->IsValidAddress(weaponAddress)) {
        return false;
    }

    bool success = true;

    // Set ItemIDHigh to -1 to enable fallback values
    success &= m_memory->Write<int>(weaponAddress + Offsets::EconItemView::m_iItemIDHigh, -1);

    // Set skin properties
    success &= m_memory->Write<int>(weaponAddress + Offsets::EconEntity::m_nFallbackPaintKit, static_cast<int>(config.skinId));
    success &= m_memory->Write<float>(weaponAddress + Offsets::EconEntity::m_flFallbackWear, config.wear);
    success &= m_memory->Write<int>(weaponAddress + Offsets::EconEntity::m_nFallbackSeed, config.seed);

    // Set StatTrak if enabled
    if (config.statTrak >= 0) {
        success &= m_memory->Write<int>(weaponAddress + Offsets::EconEntity::m_nFallbackStatTrak, config.statTrak);
    }

    // Set custom name if provided
    if (!config.customName.empty()) {
        success &= m_memory->WriteString(weaponAddress + Offsets::EconItemView::m_szCustomName, config.customName);
    }

    return success;
}

bool SkinChanger::SetKnifeSkin(uintptr_t weaponAddress, const SkinConfig& config) {
    if (!weaponAddress || !m_memory->IsValidAddress(weaponAddress)) {
        return false;
    }

    bool success = true;

    // Change weapon definition index to the desired knife
    success &= m_memory->Write<int>(weaponAddress + Offsets::EconItemView::m_iItemDefinitionIndex, static_cast<int>(config.weaponId));

    // Apply skin
    success &= SetWeaponSkin(weaponAddress, config);

    // Update viewmodel if this is the active weapon
    uintptr_t playerPawn = m_memory->GetLocalPlayerPawn();
    if (playerPawn) {
        UpdateViewModel(playerPawn, config.weaponId);
        UpdateWorldModel(weaponAddress, config.weaponId);
    }

    return success;
}

bool SkinChanger::SetGloveSkin(uintptr_t playerPawn, const SkinConfig& config) {
    if (!playerPawn || !m_memory->IsValidAddress(playerPawn)) {
        return false;
    }

    // Get economic gloves entity
    uintptr_t economicGloves = m_memory->Read<uintptr_t>(playerPawn + 0x10E8); // m_EconGloves offset
    if (!economicGloves) {
        return false;
    }

    bool success = true;

    // Set glove properties
    success &= m_memory->Write<int>(economicGloves + Offsets::EconItemView::m_iItemDefinitionIndex, static_cast<int>(config.weaponId));
    success &= m_memory->Write<int>(economicGloves + Offsets::EconItemView::m_iItemIDHigh, -1);
    success &= m_memory->Write<int>(economicGloves + Offsets::EconEntity::m_nFallbackPaintKit, static_cast<int>(config.skinId));
    success &= m_memory->Write<float>(economicGloves + Offsets::EconEntity::m_flFallbackWear, config.wear);
    success &= m_memory->Write<int>(economicGloves + Offsets::EconEntity::m_nFallbackSeed, config.seed);

    if (!config.customName.empty()) {
        success &= m_memory->WriteString(economicGloves + Offsets::EconItemView::m_szCustomName, config.customName);
    }

    return success;
}

bool SkinChanger::UpdateViewModel(uintptr_t playerPawn, WeaponId weaponId) {
    if (!playerPawn) return false;

    // Get viewmodel services
    uintptr_t viewModelServices = m_memory->Read<uintptr_t>(playerPawn + 0x1368); // m_pViewModelServices
    if (!viewModelServices) return false;

    // Get viewmodel handle
    uint32_t viewModelHandle = m_memory->Read<uint32_t>(viewModelServices + Offsets::ViewModelServices::m_hViewModel);
    if (!viewModelHandle) return false;

    // Resolve viewmodel entity
    uintptr_t viewModel = ResolveEntityHandle(viewModelHandle);
    if (!viewModel) return false;

    // Get model path for the weapon
    const WeaponInfo* weaponInfo = WeaponData::GetWeaponInfo(weaponId);
    if (!weaponInfo || weaponInfo->modelPath.empty()) return false;

    // Get model index
    int modelIndex = GetModelIndex(weaponInfo->modelPath);
    if (modelIndex <= 0) return false;

    // Update viewmodel index
    return m_memory->Write<int>(viewModel + Offsets::BaseViewModel::m_nViewModelIndex, modelIndex);
}

bool SkinChanger::UpdateWorldModel(uintptr_t weaponAddress, WeaponId weaponId) {
    // World model updates are more complex and may require additional offsets
    // For now, we'll just return true as the basic skin change should be sufficient
    UNUSED(weaponAddress);
    UNUSED(weaponId);
    return true;
}

int SkinChanger::GetModelIndex(const std::string& modelPath) {
    // In a real implementation, this would query the model precache
    // For now, we'll return a placeholder value
    UNUSED(modelPath);
    return 1;
}

uintptr_t SkinChanger::ResolveEntityHandle(uint32_t handle) {
    if (!handle) return 0;

    uintptr_t entityList = m_memory->GetEntityList();
    if (!entityList) return 0;

    // Extract index from handle
    int index = handle & 0x7FFF;

    // Get entity entry
    uintptr_t entityEntry = entityList + (index * 0x78);
    return m_memory->Read<uintptr_t>(entityEntry);
}

bool SkinChanger::ValidateMemoryAccess() {
    if (!m_memory || !m_memory->IsAttached()) {
        return false;
    }

    // Test basic memory access
    uintptr_t clientBase = m_memory->GetClientBase();
    if (!clientBase) {
        return false;
    }

    // Test reading from a known offset
    uintptr_t entityList = m_memory->GetEntityList();
    return m_memory->IsValidAddress(entityList);
}

bool SkinChanger::ValidatePlayerPawn(uintptr_t playerPawn) {
    if (!m_memory->IsValidAddress(playerPawn)) {
        return false;
    }

    // Basic validation - just check if address is valid
    // Detailed checks are done in UpdatePlayerWeapons
    return true;
}

bool SkinChanger::ValidateWeaponEntity(uintptr_t weaponAddress) {
    return IsValidWeapon(weaponAddress);
}

uint64_t SkinChanger::GetCurrentTimeMs() {
    auto now = std::chrono::steady_clock::now();
    auto duration = now.time_since_epoch();
    return std::chrono::duration_cast<std::chrono::milliseconds>(duration).count();
}

void SkinChanger::LogError(const std::string& message) {
    Console::Error("[SkinChanger] " + message);
}

void SkinChanger::LogInfo(const std::string& message) {
    Console::Info("[SkinChanger] " + message);
}
