@echo off
echo ========================================
echo   Visual Studio Environment Setup
echo ========================================
echo.

echo Procurando instalacoes do Visual Studio...

:: Try to find Visual Studio installations
set "VS_FOUND=0"
set "VS_PATH="

:: Check for Visual Studio 2022
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
    set "VS_PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
    set "VS_VERSION=2022 Community"
    set "VS_FOUND=1"
    goto :found
)

if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" (
    set "VS_PATH=C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat"
    set "VS_VERSION=2022 Professional"
    set "VS_FOUND=1"
    goto :found
)

if exist "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat" (
    set "VS_PATH=C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat"
    set "VS_VERSION=2022 Enterprise"
    set "VS_FOUND=1"
    goto :found
)

:: Check for Visual Studio 2019
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" (
    set "VS_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"
    set "VS_VERSION=2019 Community"
    set "VS_FOUND=1"
    goto :found
)

if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat" (
    set "VS_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat"
    set "VS_VERSION=2019 Professional"
    set "VS_FOUND=1"
    goto :found
)

if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Enterprise\VC\Auxiliary\Build\vcvars64.bat" (
    set "VS_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\Enterprise\VC\Auxiliary\Build\vcvars64.bat"
    set "VS_VERSION=2019 Enterprise"
    set "VS_FOUND=1"
    goto :found
)

:: Check for Build Tools
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Auxiliary\Build\vcvars64.bat" (
    set "VS_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Auxiliary\Build\vcvars64.bat"
    set "VS_VERSION=2022 Build Tools"
    set "VS_FOUND=1"
    goto :found
)

if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Auxiliary\Build\vcvars64.bat" (
    set "VS_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Auxiliary\Build\vcvars64.bat"
    set "VS_VERSION=2019 Build Tools"
    set "VS_FOUND=1"
    goto :found
)

:not_found
echo ❌ Visual Studio nao encontrado!
echo.
echo Opcoes para resolver:
echo.
echo 1. INSTALAR VISUAL STUDIO COMMUNITY (RECOMENDADO)
echo    - Baixe em: https://visualstudio.microsoft.com/downloads/
echo    - Instale com "Desktop development with C++"
echo.
echo 2. INSTALAR BUILD TOOLS (MINIMO)
echo    - Baixe em: https://visualstudio.microsoft.com/downloads/#build-tools-for-visual-studio-2022
echo    - Instale "C++ build tools"
echo.
echo 3. USAR DEVELOPER COMMAND PROMPT
echo    - Procure por "Developer Command Prompt" no menu iniciar
echo    - Execute o build.bat de la
echo.
pause
exit /b 1

:found
echo ✅ Visual Studio encontrado: %VS_VERSION%
echo    Caminho: %VS_PATH%
echo.
echo Configurando ambiente...

:: Setup Visual Studio environment
call "%VS_PATH%"

if errorlevel 1 (
    echo ❌ Erro ao configurar ambiente do Visual Studio
    pause
    exit /b 1
)

echo ✅ Ambiente configurado com sucesso!
echo.

:: Verify compiler is available
cl >nul 2>&1
if errorlevel 1 (
    echo ❌ Compiler ainda nao disponivel
    echo Tente executar em um Developer Command Prompt
    pause
    exit /b 1
)

echo ✅ Compiler disponivel!
echo.
echo Agora voce pode executar build.bat
echo.
pause
