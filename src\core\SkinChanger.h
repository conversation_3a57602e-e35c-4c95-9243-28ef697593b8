#pragma once
#include "../memory/Memory.h"
#include "../data/WeaponData.h"
#include <unordered_map>
#include <memory>

struct SkinConfig {
    WeaponId weaponId = WeaponId::AK47;
    SkinId skinId = SkinId::AK47_REDLINE;
    int seed = 0;
    float wear = 0.0001f;
    int statTrak = -1;
    std::string customName = "";
    bool enabled = false;
};

struct WeaponEntity {
    uintptr_t address = 0;
    WeaponId weaponId = WeaponId::AK47;
    bool isValid = false;
    
    // Original values for restoration
    int originalItemDefinitionIndex = 0;
    int originalItemIDHigh = 0;
    int originalPaintKit = 0;
    float originalWear = 0.0f;
    int originalSeed = 0;
    int originalStatTrak = 0;
    std::string originalCustomName = "";
};

class SkinChanger {
public:
    SkinChanger();
    ~SkinChanger();

    // Initialization
    bool Initialize(std::shared_ptr<Memory> memory);
    void Shutdown();
    bool IsInitialized() const { return m_initialized; }
    
    // Main functionality
    void Update();
    void ApplySkins();
    void RestoreOriginalSkins();
    
    // Configuration
    void SetSkinConfig(WeaponId weaponId, const SkinConfig& config);
    SkinConfig GetSkinConfig(WeaponId weaponId) const;
    void EnableSkin(WeaponId weaponId, bool enabled);
    bool IsSkinEnabled(WeaponId weaponId) const;
    
    // Knife/Glove specific
    void SetKnife(WeaponId knifeId, SkinId skinId = SkinId::KNIFE_VANILLA);
    void SetGloves(WeaponId gloveId, SkinId skinId = SkinId::GLOVE_LEATHER_WRAP);
    void DisableKnife();
    void DisableGloves();
    
    // Utility
    void ForceUpdate();
    void ReloadConfigs();
    std::vector<WeaponEntity> GetPlayerWeapons();
    
    // Settings
    void SetGlobalEnabled(bool enabled) { m_globalEnabled = enabled; }
    bool IsGlobalEnabled() const { return m_globalEnabled; }
    void SetUpdateInterval(int milliseconds) { m_updateInterval = milliseconds; }
    int GetUpdateInterval() const { return m_updateInterval; }
    
private:
    std::shared_ptr<Memory> m_memory;
    bool m_initialized = false;
    bool m_globalEnabled = true;
    int m_updateInterval = 100; // milliseconds
    
    // Configurations
    std::unordered_map<WeaponId, SkinConfig> m_skinConfigs;
    SkinConfig m_knifeConfig;
    SkinConfig m_gloveConfig;
    bool m_knifeEnabled = false;
    bool m_gloveEnabled = false;
    
    // Cached data
    std::vector<WeaponEntity> m_cachedWeapons;
    uintptr_t m_lastPlayerPawn = 0;
    uint64_t m_lastUpdateTime = 0;
    
    // Core functions
    bool UpdatePlayerWeapons();
    bool ApplySkinToWeapon(const WeaponEntity& weapon, const SkinConfig& config);
    bool RestoreWeaponSkin(const WeaponEntity& weapon);
    
    // Weapon operations
    WeaponEntity GetWeaponEntity(uintptr_t weaponAddress);
    bool IsValidWeapon(uintptr_t weaponAddress);
    WeaponId GetWeaponId(uintptr_t weaponAddress);
    
    // Skin application
    bool SetWeaponSkin(uintptr_t weaponAddress, const SkinConfig& config);
    bool SetKnifeSkin(uintptr_t weaponAddress, const SkinConfig& config);
    void ForceWeaponUpdate(uintptr_t weaponAddress);
    void ForceVisualUpdate(uintptr_t weaponAddress);
    bool SetGloveSkin(uintptr_t playerPawn, const SkinConfig& config);
    
    // Model operations
    bool UpdateViewModel(uintptr_t playerPawn, WeaponId weaponId);
    bool UpdateWorldModel(uintptr_t weaponAddress, WeaponId weaponId);
    int GetModelIndex(const std::string& modelPath);
    
    // Validation
    bool ValidateMemoryAccess();
    bool ValidatePlayerPawn(uintptr_t playerPawn);
    bool ValidateWeaponEntity(uintptr_t weaponAddress);
    
    // Entity resolution
    uintptr_t ResolveEntityHandle(uint32_t handle);

    // Utility
    uint64_t GetCurrentTimeMs();
    void LogError(const std::string& message);
    void LogInfo(const std::string& message);
};
