#include "Memory.h"
#include "../utils/Console.h"
#include "../offsets/Offsets.h"

Memory::Memory() : m_clientBase(0), m_engine2Base(0) {
    m_process = std::make_unique<Process>();
}

Memory::~Memory() {
    Detach();
}

bool Memory::AttachToCS2() {
    Console::Info("Procurando processo CS2...");
    
    // Try different process names
    std::vector<std::string> processNames = {
        "cs2.exe",
        "Counter-Strike 2.exe"
    };
    
    for (const auto& processName : processNames) {
        if (m_process->Attach(processName)) {
            if (InitializeModules()) {
                Console::Success("Anexado ao CS2 com sucesso!");
                return true;
            }
        }
    }
    
    Console::Error("Falha ao anexar ao CS2. Certifique-se de que o jogo está rodando.");
    return false;
}

bool Memory::IsAttached() const {
    return m_process && m_process->IsValid() && m_clientBase != 0;
}

void Memory::Detach() {
    if (m_process) {
        m_process->Detach();
    }
    m_clientBase = 0;
    m_engine2Base = 0;
}

bool Memory::InitializeModules() {
    Console::Info("Inicializando módulos...");
    
    // Get client.dll base
    m_clientBase = m_process->GetModuleBase("client.dll");
    if (!m_clientBase) {
        Console::Error("Falha ao encontrar client.dll");
        return false;
    }
    
    // Get engine2.dll base
    m_engine2Base = m_process->GetModuleBase("engine2.dll");
    if (!m_engine2Base) {
        Console::Error("Falha ao encontrar engine2.dll");
        return false;
    }
    
    Console::Success("Módulos inicializados:");
    Console::Info("client.dll: 0x" + std::to_string(m_clientBase));
    Console::Info("engine2.dll: 0x" + std::to_string(m_engine2Base));
    
    return ValidateModules();
}

bool Memory::ValidateModules() {
    // Validate client.dll
    auto clientSize = m_process->GetModuleSize("client.dll");
    if (clientSize < 0x1000000) { // Minimum expected size
        Console::Warning("client.dll parece ter tamanho suspeito");
    }
    
    // Validate engine2.dll
    auto engine2Size = m_process->GetModuleSize("engine2.dll");
    if (engine2Size < 0x500000) { // Minimum expected size
        Console::Warning("engine2.dll parece ter tamanho suspeito");
    }
    
    return true;
}

uintptr_t Memory::DerefPointer(uintptr_t address, const std::vector<uintptr_t>& offsets) {
    if (!IsValidAddress(address)) {
        return 0;
    }
    
    uintptr_t current = address;
    
    for (size_t i = 0; i < offsets.size(); ++i) {
        current = Read<uintptr_t>(current);
        if (!IsValidPointer(current)) {
            return 0;
        }
        
        if (i < offsets.size() - 1) {
            current += offsets[i];
        }
    }
    
    return current + offsets.back();
}

uintptr_t Memory::ResolveAddress(uintptr_t base, const std::vector<uintptr_t>& offsets) {
    if (!base || offsets.empty()) {
        return 0;
    }
    
    uintptr_t address = base + offsets[0];
    
    for (size_t i = 1; i < offsets.size(); ++i) {
        address = Read<uintptr_t>(address);
        if (!IsValidPointer(address)) {
            return 0;
        }
        address += offsets[i];
    }
    
    return address;
}

uintptr_t Memory::GetEntityList() const {
    return m_clientBase + Offsets::dwEntityList;
}

uintptr_t Memory::GetLocalPlayer() const {
    return m_clientBase + Offsets::dwLocalPlayerController;
}

uintptr_t Memory::GetLocalPlayerPawn() {
    // Method 1: Try direct pawn first
    uintptr_t directPawn = Read<uintptr_t>(m_clientBase + Offsets::dwLocalPlayerPawn);
    if (directPawn && directPawn != 0) {
        return directPawn;
    }

    // Method 2: Use LocalPlayerController -> m_hPlayerPawn (more reliable in CS2)
    uintptr_t localController = Read<uintptr_t>(m_clientBase + Offsets::dwLocalPlayerController);
    if (!localController) {
        return 0;
    }

    // Get the handle from controller
    uint32_t pawnHandle = Read<uint32_t>(localController + 0x824); // m_hPlayerPawn
    if (!pawnHandle || pawnHandle == 0xFFFFFFFF) {
        return 0;
    }

    // Resolve the handle using entity list
    uintptr_t entityList = Read<uintptr_t>(m_clientBase + Offsets::dwEntityList);
    if (!entityList) {
        return 0;
    }

    // Handle resolution for CS2
    int index = pawnHandle & 0x7FFF;
    if (index < 1 || index > 64) {
        return 0;
    }

    // Each entity entry is 0x78 bytes
    uintptr_t listEntry = Read<uintptr_t>(entityList + (8 * (index >> 9) + 0x10));
    if (!listEntry) {
        return 0;
    }

    uintptr_t entity = Read<uintptr_t>(listEntry + 0x78 * (index & 0x1FF));
    return entity;
}

uintptr_t Memory::GetEntity(int index) {
    uintptr_t entityList = GetEntityList();
    if (!entityList) {
        return 0;
    }

    // Each entity entry is 0x78 bytes apart
    uintptr_t entityEntry = entityList + (index * 0x78);
    return Read<uintptr_t>(entityEntry);
}

bool Memory::IsValidAddress(uintptr_t address) const {
    if (!address) return false;

    // Check if address is in valid range
    if (address < 0x10000 || address > 0x7FFFFFFFFFFF) {
        return false;
    }

    return true;
}

bool Memory::IsValidEntity(uintptr_t entity) const {
    if (!IsValidAddress(entity)) {
        return false;
    }

    // Additional entity validation can be added here
    return true;
}

bool Memory::IsValidPointer(uintptr_t pointer) const {
    return IsValidAddress(pointer);
}

std::string Memory::ReadString(uintptr_t address, size_t maxLength) {
    if (!IsValidAddress(address)) {
        return "";
    }

    return m_process->ReadString(address, maxLength);
}

bool Memory::WriteString(uintptr_t address, const std::string& str) {
    if (!IsValidAddress(address)) {
        return false;
    }

    return m_process->WriteString(address, str);
}

uintptr_t Memory::FindPattern(const std::string& moduleName, const std::string& pattern) {
    return m_process->PatternScan(moduleName, pattern);
}
