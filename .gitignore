# Build directories
build/
bin/
lib/
obj/
out/

# Visual Studio
.vs/
*.vcxproj.user
*.vcxproj.filters
*.sln.docstates
*.suo
*.user
*.userosscache
*.sln.docstates

# Visual Studio Code
.vscode/

# CMake
CMakeCache.txt
CMakeFiles/
cmake_install.cmake
Makefile
*.cmake

# Compiled binaries
*.exe
*.dll
*.lib
*.pdb
*.ilk
*.exp

# Object files
*.o
*.obj

# Debug files
*.idb
*.pch

# Logs
*.log

# Configuration files (may contain sensitive data)
config.json

# External dependencies
external/imgui/
external/*/

# Temporary files
*.tmp
*.temp
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
*.swp
*.swo
*~

# Backup files
*.bak
*.backup

# Package files
*.zip
*.rar
*.7z
*.tar.gz

# Runtime files
*.dmp
*.mdmp
