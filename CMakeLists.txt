cmake_minimum_required(VERSION 3.20)
project(CS2_SkinChanger)

set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set output directories
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# Include directories
include_directories(src)
include_directories(external)

# Find packages
find_package(OpenGL REQUIRED)

# ImGui files
set(IMGUI_DIR external/imgui)
file(GLOB IMGUI_SOURCES 
    ${IMGUI_DIR}/*.cpp
    ${IMGUI_DIR}/backends/imgui_impl_win32.cpp
    ${IMGUI_DIR}/backends/imgui_impl_opengl3.cpp
)

# Source files
file(GLOB_RECURSE SOURCES 
    src/*.cpp
    src/*.h
    src/*.hpp
)

# Create executable
add_executable(${PROJECT_NAME} ${SOURCES} ${IMGUI_SOURCES})

# Link libraries
target_link_libraries(${PROJECT_NAME}
    OpenGL::GL
    opengl32
    gdi32
    user32
    kernel32
    shell32
    ole32
    oleaut32
    uuid
    comdlg32
    advapi32
    ntdll
)

# Compiler flags
if(MSVC)
    target_compile_options(${PROJECT_NAME} PRIVATE
        /W3                    # Warning level 3 (instead of W4 to reduce noise)
        /wd4100               # Disable unused parameter warnings
        /wd4456               # Disable variable shadowing warnings
        /D_CRT_SECURE_NO_WARNINGS  # Disable secure CRT warnings
    )
else()
    target_compile_options(${PROJECT_NAME} PRIVATE
        -Wall -Wextra
        -Wno-unused-parameter  # Disable unused parameter warnings
    )
endif()

# Set subsystem to console for debugging
set_target_properties(${PROJECT_NAME} PROPERTIES
    WIN32_EXECUTABLE TRUE
    LINK_FLAGS "/SUBSYSTEM:CONSOLE"
)
