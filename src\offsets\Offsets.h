#pragma once
#include <cstddef>

// CS2 Offsets - Generated from cs2-dumper
// Date: 2025-07-09 12:32:27.759064900 UTC

namespace Offsets {
    // Module: client.dll
    namespace Client {
        constexpr std::ptrdiff_t dwCSGOInput = 0x1A77110;
        constexpr std::ptrdiff_t dwEntityList = 0x1A044E0;
        constexpr std::ptrdiff_t dwGameEntitySystem = 0x1B27C08;
        constexpr std::ptrdiff_t dwGameEntitySystem_highestEntityIndex = 0x20F0;
        constexpr std::ptrdiff_t dwGameRules = 0x1A68B48;
        constexpr std::ptrdiff_t dwGlobalVars = 0x184BEB0;
        constexpr std::ptrdiff_t dwGlowManager = 0x1A68298;
        constexpr std::ptrdiff_t dwLocalPlayerController = 0x1A52D20;
        constexpr std::ptrdiff_t dwLocalPlayerPawn = 0x1857F50;
        constexpr std::ptrdiff_t dwPlantedC4 = 0x1A71C40;
        constexpr std::ptrdiff_t dwPrediction = 0x1857F50;
        constexpr std::ptrdiff_t dwSensitivity = 0x1A69868;
        constexpr std::ptrdiff_t dwSensitivity_sensitivity = 0x40;
        constexpr std::ptrdiff_t dwViewAngles = 0x1A774E0;
        constexpr std::ptrdiff_t dwViewMatrix = 0x1A6D280;
        constexpr std::ptrdiff_t dwViewRender = 0x1A6DB90;
        constexpr std::ptrdiff_t dwWeaponC4 = 0x1A06570;
    }
    
    // Module: engine2.dll
    namespace Engine2 {
        constexpr std::ptrdiff_t dwBuildNumber = 0x540BE4;
        constexpr std::ptrdiff_t dwNetworkGameClient = 0x53FCE0;
        constexpr std::ptrdiff_t dwNetworkGameClient_clientTickCount = 0x368;
        constexpr std::ptrdiff_t dwNetworkGameClient_deltaTick = 0x27C;
        constexpr std::ptrdiff_t dwNetworkGameClient_isBackgroundMap = 0x281447;
        constexpr std::ptrdiff_t dwNetworkGameClient_localPlayer = 0xF0;
        constexpr std::ptrdiff_t dwNetworkGameClient_maxClients = 0x238;
        constexpr std::ptrdiff_t dwNetworkGameClient_serverTickCount = 0x36C;
        constexpr std::ptrdiff_t dwNetworkGameClient_signOnState = 0x228;
        constexpr std::ptrdiff_t dwWindowHeight = 0x62359C;
        constexpr std::ptrdiff_t dwWindowWidth = 0x623598;
    }
    
    // Weapon Services
    namespace WeaponServices {
        constexpr std::ptrdiff_t m_hMyWeapons = 0x40;
        constexpr std::ptrdiff_t m_hActiveWeapon = 0x58;
        constexpr std::ptrdiff_t m_hLastWeapon = 0x5C;
        constexpr std::ptrdiff_t m_iAmmo = 0x60;
    }
    
    // ViewModel Services
    namespace ViewModelServices {
        constexpr std::ptrdiff_t m_hViewModel = 0x40;
    }
    
    // BasePlayerPawn
    namespace BasePlayerPawn {
        constexpr std::ptrdiff_t m_pWeaponServices = 0x11A8;
    }

    // EconEntity (Weapon/Item)
    namespace EconEntity {
        constexpr std::ptrdiff_t m_AttributeManager = 0x1148;
        constexpr std::ptrdiff_t m_OriginalOwnerXuidLow = 0x15F0;
        constexpr std::ptrdiff_t m_OriginalOwnerXuidHigh = 0x15F4;
        constexpr std::ptrdiff_t m_nFallbackPaintKit = 0x15F8;
        constexpr std::ptrdiff_t m_nFallbackSeed = 0x15FC;
        constexpr std::ptrdiff_t m_flFallbackWear = 0x1600;
        constexpr std::ptrdiff_t m_nFallbackStatTrak = 0x1604;
        constexpr std::ptrdiff_t m_bClientside = 0x1608;
        constexpr std::ptrdiff_t m_bParticleSystemsCreated = 0x1609;
        constexpr std::ptrdiff_t m_vecAttachedParticles = 0x1610;
        constexpr std::ptrdiff_t m_hViewmodelAttachment = 0x1628;
        constexpr std::ptrdiff_t m_iOldTeam = 0x162C;
        constexpr std::ptrdiff_t m_bAttachmentDirty = 0x1630;
        constexpr std::ptrdiff_t m_nUnloadedModelIndex = 0x1634;
    }

    // EconItemView
    namespace EconItemView {
        constexpr std::ptrdiff_t m_iItemDefinitionIndex = 0x1BA;
        constexpr std::ptrdiff_t m_iEntityQuality = 0x1BC;
        constexpr std::ptrdiff_t m_iEntityLevel = 0x1C0;
        constexpr std::ptrdiff_t m_iItemID = 0x1C8;
        constexpr std::ptrdiff_t m_iItemIDHigh = 0x1D0;
        constexpr std::ptrdiff_t m_iItemIDLow = 0x1D4;
        constexpr std::ptrdiff_t m_iAccountID = 0x1D8;
        constexpr std::ptrdiff_t m_iInventoryPosition = 0x1DC;
        constexpr std::ptrdiff_t m_bInitialized = 0x1E8;
        constexpr std::ptrdiff_t m_AttributeList = 0x210;
        constexpr std::ptrdiff_t m_NetworkedDynamicAttributes = 0x270;
        constexpr std::ptrdiff_t m_szCustomName = 0x2D0;
    }
    
    // BaseViewModel
    namespace BaseViewModel {
        constexpr std::ptrdiff_t m_nViewModelIndex = 0xF9C;
        constexpr std::ptrdiff_t m_nAnimationParity = 0xFA0;
        constexpr std::ptrdiff_t m_flAnimationStartTime = 0xFA4;
        constexpr std::ptrdiff_t m_hWeapon = 0xFA8;
        constexpr std::ptrdiff_t m_hControlPanel = 0xFE8;
    }
    
    // CSWeaponBase
    namespace CSWeaponBase {
        constexpr std::ptrdiff_t m_nViewModelIndex = 0x177C;
        constexpr std::ptrdiff_t m_bReloadsWithClips = 0x1780;
        constexpr std::ptrdiff_t m_flTimeWeaponIdle = 0x1784;
        constexpr std::ptrdiff_t m_bFireOnEmpty = 0x1788;
    }
    
    // Convenience aliases for main offsets
    constexpr std::ptrdiff_t dwEntityList = Client::dwEntityList;
    constexpr std::ptrdiff_t dwLocalPlayerController = Client::dwLocalPlayerController;
    constexpr std::ptrdiff_t dwLocalPlayerPawn = Client::dwLocalPlayerPawn;
    constexpr std::ptrdiff_t dwViewMatrix = Client::dwViewMatrix;
    constexpr std::ptrdiff_t dwViewAngles = Client::dwViewAngles;
}
