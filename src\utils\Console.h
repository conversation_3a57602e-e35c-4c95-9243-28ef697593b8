#pragma once
#include <string>
#include <Windows.h>

class Console {
public:
    static void Initialize();
    static void SetTitle(const std::string& title);
    static void Clear();
    static void Pause();
    
    // Colored output methods
    static void Info(const std::string& message);
    static void Success(const std::string& message);
    static void Warning(const std::string& message);
    static void Error(const std::string& message);
    static void Debug(const std::string& message);
    
    // Colors
    enum Color {
        BLACK = 0,
        DARK_BLUE = 1,
        DARK_GREEN = 2,
        DARK_CYAN = 3,
        DARK_RED = 4,
        DARK_MAGENTA = 5,
        DARK_YELLOW = 6,
        LIGHT_GRAY = 7,
        DARK_GRAY = 8,
        BLUE = 9,
        GREEN = 10,
        CYAN = 11,
        RED = 12,
        MAGENTA = 13,
        YELLOW = 14,
        WHITE = 15
    };
    
private:
    static HANDLE hConsole;
    static void SetColor(Color color);
    static void ResetColor();
};
