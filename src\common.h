#pragma once

// Common includes and definitions for the project

// Suppress common warnings
#ifdef _MSC_VER
    #pragma warning(push)
    #pragma warning(disable: 4100) // unreferenced formal parameter
    #pragma warning(disable: 4456) // declaration hides previous local declaration
    #pragma warning(disable: 4996) // deprecated function warnings
    #pragma warning(disable: 4005) // macro redefinition
    #ifndef _CRT_SECURE_NO_WARNINGS
        #define _CRT_SECURE_NO_WARNINGS
    #endif
#endif

// Windows includes
#include <Windows.h>

// Standard library includes
#include <string>
#include <vector>
#include <memory>
#include <unordered_map>
#include <map>
#include <chrono>
#include <thread>
#include <iostream>
#include <fstream>
#include <sstream>

// Utility macros
#define UNUSED(x) ((void)(x))

// Restore warnings
#ifdef _MSC_VER
    #pragma warning(pop)
#endif
